{"src\\main\\java\\com\\rifsxd\\ksunext\\KsuService.java": ["TAG:com.rifsxd.ksunext.ui.KsuService", "KsuService:com.rifsxd.ksunext.ui", "getPackages:com.rifsxd.ksunext.ui.KsuService.Stub", "onBind:com.rifsxd.ksunext.ui.KsuService", "getUserIds:com.rifsxd.ksunext.ui.KsuService", "getInstalledPackagesAll:com.rifsxd.ksunext.ui.KsuService", "getInstalledPackagesAsUser:com.rifsxd.ksunext.ui.KsuService", "Stub:com.rifsxd.ksunext.ui.KsuService"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\theme\\Color.kt": ["DARK_BLUE_TEXT:com.rifsxd.ksunext.ui.theme", "PRIMARY_BLUE:com.rifsxd.ksunext.ui.theme", "LIGHT_BLUE_CONTAINER:com.rifsxd.ksunext.ui.theme", "AMOLED_BLACK:com.rifsxd.ksunext.ui.theme", "LIGHT_BLUE_GREY_CONTAINER:com.rifsxd.ksunext.ui.theme", "GREEN:com.rifsxd.ksunext.ui.theme", "YELLOW:com.rifsxd.ksunext.ui.theme", "ORANGE:com.rifsxd.ksunext.ui.theme", "SECONDARY_BLUE_GREY:com.rifsxd.ksunext.ui.theme", "RED:com.rifsxd.ksunext.ui.theme"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\screen\\BottomBarDestination.kt": ["iconSelected:com.rifsxd.ksunext.ui.screen.BottomBarDestination", "Home:com.rifsxd.ksunext.ui.screen.BottomBarDestination", "valueOf:com.rifsxd.ksunext.ui.screen.BottomBarDestination", "BottomBarDestination:com.rifsxd.ksunext.ui.screen", "rootRequired:com.rifsxd.ksunext.ui.screen.BottomBarDestination", "values:com.rifsxd.ksunext.ui.screen.BottomBarDestination", "direction:com.rifsxd.ksunext.ui.screen.BottomBarDestination", "label:com.rifsxd.ksunext.ui.screen.BottomBarDestination", "iconNotSelected:com.rifsxd.ksunext.ui.screen.BottomBarDestination", "entries:com.rifsxd.ksunext.ui.screen.BottomBarDestination"], "src\\main\\java\\com\\rifsxd\\ksunext\\Natives.kt": ["component11:com.rifsxd.ksunext.Natives.Profile", "copy:com.rifsxd.ksunext.Natives.Profile", "context:com.rifsxd.ksunext.Natives.Profile", "isSuEnabled:com.rifsxd.ksunext.Natives", "component6:com.rifsxd.ksunext.Natives.Profile", "Namespace:com.rifsxd.ksunext.Natives.Profile", "NOBODY_UID:com.rifsxd.ksunext.Natives", "MINIMAL_SUPPORTED_MANAGER_UID:com.rifsxd.ksunext.Natives", "Natives:com.rifsxd.ksunext", "NON_ROOT_DEFAULT_PROFILE_KEY:com.rifsxd.ksunext.Natives", "uidShouldUmount:com.rifsxd.ksunext.Natives", "getAppProfile:com.rifsxd.ksunext.Natives", "currentUid:com.rifsxd.ksunext.Natives.Profile", "component1:com.rifsxd.ksunext.Natives.Profile", "component5:com.rifsxd.ksunext.Natives.Profile", "KERNEL_SU_DOMAIN:com.rifsxd.ksunext.Natives", "isZygiskEnabled:com.rifsxd.ksunext.Natives", "isDefaultUmountModules:com.rifsxd.ksunext.Natives", "uid:com.rifsxd.ksunext.Natives.Profile", "MINIMAL_SUPPORTED_KERNEL:com.rifsxd.ksunext.Natives", "component12:com.rifsxd.ksunext.Natives.Profile", "setAppProfile:com.rifsxd.ksunext.Natives", "setDefaultUmountModules:com.rifsxd.ksunext.Natives", "rootUseDefault:com.rifsxd.ksunext.Natives.Profile", "getManagerUid:com.rifsxd.ksunext.Natives", "component7:com.rifsxd.ksunext.Natives.Profile", "isSafeMode:com.rifsxd.ksunext.Natives", "component2:com.rifsxd.ksunext.Natives.Profile", "values:com.rifsxd.ksunext.Natives.Profile.Namespace", "name:com.rifsxd.ksunext.Natives.Profile", "ROOT_GID:com.rifsxd.ksunext.Natives", "allowSu:com.rifsxd.ksunext.Natives.Profile", "entries:com.rifsxd.ksunext.Natives.Profile.Namespace", "nonRootUseDefault:com.rifsxd.ksunext.Natives.Profile", "component13:com.rifsxd.ksunext.Natives.Profile", "setSuEnabled:com.rifsxd.ksunext.Natives", "INHERITED:com.rifsxd.ksunext.Natives.Profile.Namespace", "rules:com.rifsxd.ksunext.Natives.Profile", "Profile:com.rifsxd.ksunext.Natives", "GLOBAL:com.rifsxd.ksunext.Natives.Profile.Namespace", "capabilities:com.rifsxd.ksunext.Natives.Profile", "component8:com.rifsxd.ksunext.Natives.Profile", "version:com.rifsxd.ksunext.Natives", "component3:com.rifsxd.ksunext.Natives.Profile", "getHookMode:com.rifsxd.ksunext.Natives", "gid:com.rifsxd.ksunext.Natives.Profile", "groups:com.rifsxd.ksunext.Natives.Profile", "INDIVIDUAL:com.rifsxd.ksunext.Natives.Profile.Namespace", "MINIMAL_SUPPORTED_SU_COMPAT:com.rifsxd.ksunext.Natives", "allowList:com.rifsxd.ksunext.Natives", "rootTemplate:com.rifsxd.ksunext.Natives.Profile", "component14:com.rifsxd.ksunext.Natives.Profile", "umountModules:com.rifsxd.ksunext.Natives.Profile", "ROOT_UID:com.rifsxd.ksunext.Natives", "component10:com.rifsxd.ksunext.Natives.Profile", "valueOf:com.rifsxd.ksunext.Natives.Profile.Namespace", "requireNewKernel:com.rifsxd.ksunext.Natives", "component9:com.rifsxd.ksunext.Natives.Profile", "MINIMAL_SUPPORTED_KERNEL_LKM:com.rifsxd.ksunext.Natives", "MINIMAL_SUPPORTED_HOOK_MODE:com.rifsxd.ksunext.Natives", "isLkmMode:com.rifsxd.ksunext.Natives", "component4:com.rifsxd.ksunext.Natives.Profile", "becomeManager:com.rifsxd.ksunext.Natives", "namespace:com.rifsxd.ksunext.Natives.Profile"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\util\\LocaleHelper.kt": ["useSystemLanguageSettings:com.rifsxd.ksunext.ui.util.LocaleHelper", "launchSystemLanguageSettings:com.rifsxd.ksunext.ui.util.LocaleHelper", "restartActivity:com.rifsxd.ksunext.ui.util.LocaleHelper", "setLocale:com.rifsxd.ksunext.ui.util.LocaleHelper", "updateResourcesLegacy:com.rifsxd.ksunext.ui.util.LocaleHelper", "LocaleHelper:com.rifsxd.ksunext.ui.util", "parseLocaleTag:com.rifsxd.ksunext.ui.util.LocaleHelper", "updateResources:com.rifsxd.ksunext.ui.util.LocaleHelper", "applyLanguage:com.rifsxd.ksunext.ui.util.LocaleHelper", "getCurrentAppLocale:com.rifsxd.ksunext.ui.util.LocaleHelper"], "build\\generated\\aidl_source_output_dir\\debug\\out\\com\\rifsxd\\ksunext\\IKsuInterface.java": ["getInterfaceDescriptor:com.rifsxd.ksunext.IKsuInterface.Stub.Proxy", "IKsuInterface:com.rifsxd.ksunext", "_Parcel:com.rifsxd.ksunext.IKsuInterface", "Default:com.rifsxd.ksunext.IKsuInterface", "mRemote:com.rifsxd.ksunext.IKsuInterface.Stub.Proxy", "readTypedObject:com.rifsxd.ksunext.IKsuInterface._Parcel", "asBinder:com.rifsxd.ksunext.IKsuInterface.Stub.Proxy", "onTransact:com.rifsxd.ksunext.IKsuInterface.Stub", "asBinder:com.rifsxd.ksunext.IKsuInterface.Stub", "Stub:com.rifsxd.ksunext.IKsuInterface", "Proxy:com.rifsxd.ksunext.IKsuInterface.Stub", "writeTypedObject:com.rifsxd.ksunext.IKsuInterface._Parcel", "asBinder:com.rifsxd.ksunext.IKsuInterface.Default", "getPackages:com.rifsxd.ksunext.IKsuInterface", "TRANSACTION_getPackages:com.rifsxd.ksunext.IKsuInterface.Stub", "getPackages:com.rifsxd.ksunext.IKsuInterface.Default", "getPackages:com.rifsxd.ksunext.IKsuInterface.Stub.Proxy", "DESCRIPTOR:com.rifsxd.ksunext.IKsuInterface", "asInterface:com.rifsxd.ksunext.IKsuInterface.Stub"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\theme\\Theme.kt": ["blend:com.rifsxd.ksunext.ui.theme", "KernelSUTheme:com.rifsxd.ksunext.ui.theme", "SystemBarStyle:com.rifsxd.ksunext.ui.theme", "CustomLightColorScheme:com.rifsxd.ksunext.ui.theme"], "build\\generated\\ksp\\debug\\kotlin\\com\\ramcosta\\composedestinations\\generated\\destinations\\HomeScreenDestination.kt": ["invoke:com.ramcosta.composedestinations.generated.destinations.HomeScreenDestination", "HomeScreenDestination:com.ramcosta.composedestinations.generated.destinations", "Content:com.ramcosta.composedestinations.generated.destinations.HomeScreenDestination", "baseRoute:com.ramcosta.composedestinations.generated.destinations.HomeScreenDestination", "route:com.ramcosta.composedestinations.generated.destinations.HomeScreenDestination"], "src\\main\\java\\com\\rifsxd\\ksunext\\Kernels.kt": ["copy:com.rifsxd.ksunext.KernelVersion", "toString:com.rifsxd.ksunext.KernelVersion", "isGKI:com.rifsxd.ksunext.KernelVersion", "isULegacy:com.rifsxd.ksunext.KernelVersion", "major:com.rifsxd.ksunext.KernelVersion", "isLegacy:com.rifsxd.ksunext.KernelVersion", "isGKI1:com.rifsxd.ksunext.KernelVersion", "parseKernelVersion:com.rifsxd.ksunext", "getKernelVersion:com.rifsxd.ksunext", "component1:com.rifsxd.ksunext.KernelVersion", "patchLevel:com.rifsxd.ksunext.KernelVersion", "subLevel:com.rifsxd.ksunext.KernelVersion", "KernelVersion:com.rifsxd.ksunext", "component2:com.rifsxd.ksunext.KernelVersion", "component3:com.rifsxd.ksunext.KernelVersion"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\component\\SettingsItem.kt": ["RadioItem:com.rifsxd.ksunext.ui.component", "SwitchItem:com.rifsxd.ksunext.ui.component"], "src\\main\\java\\com\\rifsxd\\ksunext\\KernelSUApplication.kt": ["okhttpClient:com.rifsxd.ksunext.KernelSUApplication", "onCreate:com.rifsxd.ksunext.KernelSUApplication", "ksuApp:com.rifsxd.ksunext", "KernelSUApplication:com.rifsxd.ksunext"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\component\\KeyEventBlocker.kt": ["KeyEventBlocker:com.rifsxd.ksunext.ui.component"], "src\\main\\java\\com\\rifsxd\\ksunext\\profile\\Capabilities.kt": ["CAP_SYS_NICE:com.rifsxd.ksunext.profile.Capabilities", "CAP_NET_RAW:com.rifsxd.ksunext.profile.Capabilities", "desc:com.rifsxd.ksunext.profile.Capabilities", "CAP_AUDIT_READ:com.rifsxd.ksunext.profile.Capabilities", "CAP_MAC_ADMIN:com.rifsxd.ksunext.profile.Capabilities", "CAP_SETUID:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYS_RAWIO:com.rifsxd.ksunext.profile.Capabilities", "CAP_DAC_READ_SEARCH:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYS_RESOURCE:com.rifsxd.ksunext.profile.Capabilities", "Capabilities:com.rifsxd.ksunext.profile", "CAP_KILL:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYS_ADMIN:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYS_BOOT:com.rifsxd.ksunext.profile.Capabilities", "CAP_IPC_LOCK:com.rifsxd.ksunext.profile.Capabilities", "CAP_CHOWN:com.rifsxd.ksunext.profile.Capabilities", "CAP_DAC_OVERRIDE:com.rifsxd.ksunext.profile.Capabilities", "valueOf:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYS_TIME:com.rifsxd.ksunext.profile.Capabilities", "CAP_SETFCAP:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYS_CHROOT:com.rifsxd.ksunext.profile.Capabilities", "CAP_FSETID:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYS_TTY_CONFIG:com.rifsxd.ksunext.profile.Capabilities", "CAP_NET_BIND_SERVICE:com.rifsxd.ksunext.profile.Capabilities", "CAP_NET_ADMIN:com.rifsxd.ksunext.profile.Capabilities", "CAP_AUDIT_WRITE:com.rifsxd.ksunext.profile.Capabilities", "CAP_LINUX_IMMUTABLE:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYS_PTRACE:com.rifsxd.ksunext.profile.Capabilities", "CAP_MKNOD:com.rifsxd.ksunext.profile.Capabilities", "CAP_MAC_OVERRIDE:com.rifsxd.ksunext.profile.Capabilities", "CAP_SETPCAP:com.rifsxd.ksunext.profile.Capabilities", "CAP_IPC_OWNER:com.rifsxd.ksunext.profile.Capabilities", "entries:com.rifsxd.ksunext.profile.Capabilities", "CAP_WAKE_ALARM:com.rifsxd.ksunext.profile.Capabilities", "cap:com.rifsxd.ksunext.profile.Capabilities", "display:com.rifsxd.ksunext.profile.Capabilities", "CAP_BLOCK_SUSPEND:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYSLOG:com.rifsxd.ksunext.profile.Capabilities", "CAP_AUDIT_CONTROL:com.rifsxd.ksunext.profile.Capabilities", "CAP_NET_BROADCAST:com.rifsxd.ksunext.profile.Capabilities", "CAP_BPF:com.rifsxd.ksunext.profile.Capabilities", "CAP_LEASE:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYS_MODULE:com.rifsxd.ksunext.profile.Capabilities", "CAP_SYS_PACCT:com.rifsxd.ksunext.profile.Capabilities", "CAP_FOWNER:com.rifsxd.ksunext.profile.Capabilities", "CAP_SETGID:com.rifsxd.ksunext.profile.Capabilities", "CAP_PERFMON:com.rifsxd.ksunext.profile.Capabilities", "CAP_CHECKPOINT_RESTORE:com.rifsxd.ksunext.profile.Capabilities", "values:com.rifsxd.ksunext.profile.Capabilities"], "build\\generated\\ksp\\debug\\kotlin\\com\\ramcosta\\composedestinations\\generated\\navgraphs\\RootNavGraph.kt": ["destinations:com.ramcosta.composedestinations.generated.navgraphs.RootNavGraph", "RootNavGraph:com.ramcosta.composedestinations.generated.navgraphs", "defaultTransitions:com.ramcosta.composedestinations.generated.navgraphs.RootNavGraph", "defaultStartDirection:com.ramcosta.composedestinations.generated.navgraphs.RootNavGraph", "route:com.ramcosta.composedestinations.generated.navgraphs.RootNavGraph", "startRoute:com.ramcosta.composedestinations.generated.navgraphs.RootNavGraph"], "build\\generated\\source\\buildConfig\\debug\\com\\rifsxd\\ksunext\\BuildConfig.java": ["DEBUG:com.rifsxd.ksunext.BuildConfig", "VERSION_NAME:com.rifsxd.ksunext.BuildConfig", "BuildConfig:com.rifsxd.ksunext", "BUILD_TYPE:com.rifsxd.ksunext.BuildConfig", "VERSION_CODE:com.rifsxd.ksunext.BuildConfig", "APPLICATION_ID:com.rifsxd.ksunext.BuildConfig"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\util\\KsuCli.kt": ["allowlistRestore:com.rifsxd.ksunext.ui.util", "runModuleAction:com.rifsxd.ksunext.ui.util", "getRootShell:com.rifsxd.ksunext.ui.util", "copy:com.rifsxd.ksunext.ui.util.LkmSelection.LkmUri", "getModuleCount:com.rifsxd.ksunext.ui.util", "TAG:com.rifsxd.ksunext.ui.util", "forceStopApp:com.rifsxd.ksunext.ui.util", "KsuCli:com.rifsxd.ksunext.ui.util", "moduleMigration:com.rifsxd.ksunext.ui.util", "restoreBoot:com.rifsxd.ksunext.ui.util", "copy:com.rifsxd.ksunext.ui.util.LkmSelection.KmiString", "susfsSUS_SU_Mode:com.rifsxd.ksunext.ui.util", "withNewRootShell:com.rifsxd.ksunext.ui.util", "KmiString:com.rifsxd.ksunext.ui.util.LkmSelection", "ksuDaemonMagicPath:com.rifsxd.ksunext.ui.util", "uninstallModule:com.rifsxd.ksunext.ui.util", "SHELL:com.rifsxd.ksunext.ui.util.KsuCli", "shrinkModules:com.rifsxd.ksunext.ui.util", "execKsud:com.rifsxd.ksunext.ui.util", "getSuSFSVariant:com.rifsxd.ksunext.ui.util", "component1:com.rifsxd.ksunext.ui.util.LkmSelection.KmiString", "LkmUri:com.rifsxd.ksunext.ui.util.LkmSelection", "getSuSFSDaemonPath:com.rifsxd.ksunext.ui.util", "susfsSUS_SU_0:com.rifsxd.ksunext.ui.util", "currentMountSystem:com.rifsxd.ksunext.ui.util", "component3:com.rifsxd.ksunext.ui.util.FlashResult", "getFileName:com.rifsxd.ksunext.ui.util", "overlayFsAvailable:com.rifsxd.ksunext.ui.util", "component1:com.rifsxd.ksunext.ui.util.LkmSelection.LkmUri", "moduleRestore:com.rifsxd.ksunext.ui.util", "restartApp:com.rifsxd.ksunext.ui.util", "installBoot:com.rifsxd.ksunext.ui.util", "getSuSFSFeatures:com.rifsxd.ksunext.ui.util", "toggleModule:com.rifsxd.ksunext.ui.util", "isSepolicyValid:com.rifsxd.ksunext.ui.util", "listModules:com.rifsxd.ksunext.ui.util", "getSepolicy:com.rifsxd.ksunext.ui.util", "GLOBAL_MNT_SHELL:com.rifsxd.ksunext.ui.util.KsuCli", "restoreModule:com.rifsxd.ksunext.ui.util", "getAppProfileTemplate:com.rifsxd.ksunext.ui.util", "getCurrentKmi:com.rifsxd.ksunext.ui.util", "isSuCompatDisabled:com.rifsxd.ksunext.ui.util", "createRootShell:com.rifsxd.ksunext.ui.util", "component2:com.rifsxd.ksunext.ui.util.FlashResult", "value:com.rifsxd.ksunext.ui.util.LkmSelection.KmiString", "setAppProfileTemplate:com.rifsxd.ksunext.ui.util", "allowlistBackup:com.rifsxd.ksunext.ui.util", "hasMagisk:com.rifsxd.ksunext.ui.util", "isAbDevice:com.rifsxd.ksunext.ui.util", "getSuSFSVersion:com.rifsxd.ksunext.ui.util", "moduleBackupDir:com.rifsxd.ksunext.ui.util", "err:com.rifsxd.ksunext.ui.util.FlashResult", "getModuleSize:com.rifsxd.ksunext.ui.util", "readMountSystemFile:com.rifsxd.ksunext.ui.util", "flashModule:com.rifsxd.ksunext.ui.util", "copy:com.rifsxd.ksunext.ui.util.FlashResult", "ksuDaemonOverlayfsPath:com.rifsxd.ksunext.ui.util", "zygiskRequired:com.rifsxd.ksunext.ui.util", "uninstallPermanently:com.rifsxd.ksunext.ui.util", "deleteAppProfileTemplate:com.rifsxd.ksunext.ui.util", "susfsSUS_SU_2:com.rifsxd.ksunext.ui.util", "code:com.rifsxd.ksunext.ui.util.FlashResult", "showReboot:com.rifsxd.ksunext.ui.util.FlashResult", "BUSYBOX:com.rifsxd.ksunext.ui.util", "updateMountSystemFile:com.rifsxd.ksunext.ui.util", "launchApp:com.rifsxd.ksunext.ui.util", "reboot:com.rifsxd.ksunext.ui.util", "getSuSFS:com.rifsxd.ksunext.ui.util", "LkmSelection:com.rifsxd.ksunext.ui.util", "uri:com.rifsxd.ksunext.ui.util.LkmSelection.LkmUri", "component1:com.rifsxd.ksunext.ui.util.FlashResult", "getKsuDaemonPath:com.rifsxd.ksunext.ui.util", "flashWithIO:com.rifsxd.ksunext.ui.util", "KmiNone:com.rifsxd.ksunext.ui.util.LkmSelection", "FlashResult:com.rifsxd.ksunext.ui.util", "getSuperuserCount:com.rifsxd.ksunext.ui.util", "setSepolicy:com.rifsxd.ksunext.ui.util", "rootAvailable:com.rifsxd.ksunext.ui.util", "getSupportedKmis:com.rifsxd.ksunext.ui.util", "listAppProfileTemplates:com.rifsxd.ksunext.ui.util", "moduleBackup:com.rifsxd.ksunext.ui.util", "install:com.rifsxd.ksunext.ui.util", "isInitBoot:com.rifsxd.ksunext.ui.util"], "src\\main\\java\\com\\rifsxd\\ksunext\\profile\\Groups.kt": ["RADIO:com.rifsxd.ksunext.profile.Groups", "desc:com.rifsxd.ksunext.profile.Groups", "READPROC:com.rifsxd.ksunext.profile.Groups", "PRNG_SEEDER:com.rifsxd.ksunext.profile.Groups", "BIN:com.rifsxd.ksunext.profile.Groups", "LLKD:com.rifsxd.ksunext.profile.Groups", "DICED:com.rifsxd.ksunext.profile.Groups", "USB:com.rifsxd.ksunext.profile.Groups", "SDCARD_AV:com.rifsxd.ksunext.profile.Groups", "DAEMON:com.rifsxd.ksunext.profile.Groups", "CAMERA:com.rifsxd.ksunext.profile.Groups", "AUTOMOTIVE_EVS:com.rifsxd.ksunext.profile.Groups", "EXTERNAL_STORAGE:com.rifsxd.ksunext.profile.Groups", "UWB:com.rifsxd.ksunext.profile.Groups", "valueOf:com.rifsxd.ksunext.profile.Groups", "INET:com.rifsxd.ksunext.profile.Groups", "SDCARD_RW:com.rifsxd.ksunext.profile.Groups", "CREDSTORE:com.rifsxd.ksunext.profile.Groups", "NET_ADMIN:com.rifsxd.ksunext.profile.Groups", "DBUS:com.rifsxd.ksunext.profile.Groups", "AUDIOSERVER:com.rifsxd.ksunext.profile.Groups", "NET_BW_ACCT:com.rifsxd.ksunext.profile.Groups", "UHID:com.rifsxd.ksunext.profile.Groups", "LOGD:com.rifsxd.ksunext.profile.Groups", "GPU_SERVICE:com.rifsxd.ksunext.profile.Groups", "APP:com.rifsxd.ksunext.profile.Groups", "NOBODY:com.rifsxd.ksunext.profile.Groups", "SYS:com.rifsxd.ksunext.profile.Groups", "DNS:com.rifsxd.ksunext.profile.Groups", "MEDIA_CODEC:com.rifsxd.ksunext.profile.Groups", "MEDIA_RW:com.rifsxd.ksunext.profile.Groups", "GRAPHICS:com.rifsxd.ksunext.profile.Groups", "TOMBSTONED:com.rifsxd.ksunext.profile.Groups", "VIRTUALIZATIONSERVICE:com.rifsxd.ksunext.profile.Groups", "JC_WEAVER:com.rifsxd.ksunext.profile.Groups", "NFC:com.rifsxd.ksunext.profile.Groups", "NET_RAW:com.rifsxd.ksunext.profile.Groups", "NET_BT_ADMIN:com.rifsxd.ksunext.profile.Groups", "GPS:com.rifsxd.ksunext.profile.Groups", "DIAG:com.rifsxd.ksunext.profile.Groups", "ROOT:com.rifsxd.ksunext.profile.Groups", "METRICSD:com.rifsxd.ksunext.profile.Groups", "WAKELOCK:com.rifsxd.ksunext.profile.Groups", "UNUSED2:com.rifsxd.ksunext.profile.Groups", "MOUNT:com.rifsxd.ksunext.profile.Groups", "VPN:com.rifsxd.ksunext.profile.Groups", "WEBVIEW_ZYGOTE:com.rifsxd.ksunext.profile.Groups", "MDNSR:com.rifsxd.ksunext.profile.Groups", "SDCARD_R:com.rifsxd.ksunext.profile.Groups", "SHARED_RELRO:com.rifsxd.ksunext.profile.Groups", "NET_BT:com.rifsxd.ksunext.profile.Groups", "CLAT:com.rifsxd.ksunext.profile.Groups", "SDCARD_PICS:com.rifsxd.ksunext.profile.Groups", "WEBSERV:com.rifsxd.ksunext.profile.Groups", "LMKD:com.rifsxd.ksunext.profile.Groups", "MISC:com.rifsxd.ksunext.profile.Groups", "METRICS_COLL:com.rifsxd.ksunext.profile.Groups", "TRUNKS:com.rifsxd.ksunext.profile.Groups", "SYSTEM:com.rifsxd.ksunext.profile.Groups", "CACHE:com.rifsxd.ksunext.profile.Groups", "UNUSED1:com.rifsxd.ksunext.profile.Groups", "DNS_TETHER:com.rifsxd.ksunext.profile.Groups", "DMESGD:com.rifsxd.ksunext.profile.Groups", "GSID:com.rifsxd.ksunext.profile.Groups", "values:com.rifsxd.ksunext.profile.Groups", "INCIDENTD:com.rifsxd.ksunext.profile.Groups", "FSVERITY_CERT:com.rifsxd.ksunext.profile.Groups", "VEHICLE_NETWORK:com.rifsxd.ksunext.profile.Groups", "CONTEXT_HUB:com.rifsxd.ksunext.profile.Groups", "LOOP_RADIO:com.rifsxd.ksunext.profile.Groups", "MEDIA_OBB:com.rifsxd.ksunext.profile.Groups", "READTRACEFS:com.rifsxd.ksunext.profile.Groups", "COMPASS:com.rifsxd.ksunext.profile.Groups", "DRMRPC:com.rifsxd.ksunext.profile.Groups", "MEDIA_IMAGE:com.rifsxd.ksunext.profile.Groups", "SDCARD_ALL:com.rifsxd.ksunext.profile.Groups", "EXT_DATA_RW:com.rifsxd.ksunext.profile.Groups", "MTP:com.rifsxd.ksunext.profile.Groups", "AUDIO:com.rifsxd.ksunext.profile.Groups", "DEBUGGERD:com.rifsxd.ksunext.profile.Groups", "NET_BT_STACK:com.rifsxd.ksunext.profile.Groups", "MEDIA_AUDIO:com.rifsxd.ksunext.profile.Groups", "TLSDATE:com.rifsxd.ksunext.profile.Groups", "SECURITY_LOG_WRITER:com.rifsxd.ksunext.profile.Groups", "MEDIA:com.rifsxd.ksunext.profile.Groups", "DHCP:com.rifsxd.ksunext.profile.Groups", "EXT_OBB_RW:com.rifsxd.ksunext.profile.Groups", "entries:com.rifsxd.ksunext.profile.Groups", "STATSD:com.rifsxd.ksunext.profile.Groups", "HSM:com.rifsxd.ksunext.profile.Groups", "RESERVED_DISK:com.rifsxd.ksunext.profile.Groups", "BLUETOOTH:com.rifsxd.ksunext.profile.Groups", "IORAPD:com.rifsxd.ksunext.profile.Groups", "gid:com.rifsxd.ksunext.profile.Groups", "ESE:com.rifsxd.ksunext.profile.Groups", "SHELL:com.rifsxd.ksunext.profile.Groups", "THREAD_NETWORK:com.rifsxd.ksunext.profile.Groups", "PACKAGE_INFO:com.rifsxd.ksunext.profile.Groups", "SECURE_ELEMENT:com.rifsxd.ksunext.profile.Groups", "NETWORK_STACK:com.rifsxd.ksunext.profile.Groups", "INSTALL:com.rifsxd.ksunext.profile.Groups", "OTA_UPDATE:com.rifsxd.ksunext.profile.Groups", "EVERYBODY:com.rifsxd.ksunext.profile.Groups", "WIFI:com.rifsxd.ksunext.profile.Groups", "INPUT:com.rifsxd.ksunext.profile.Groups", "Groups:com.rifsxd.ksunext.profile", "LOG:com.rifsxd.ksunext.profile.Groups", "MEDIA_DRM:com.rifsxd.ksunext.profile.Groups", "FIREWALL:com.rifsxd.ksunext.profile.Groups", "JC_IDENTITYCRED:com.rifsxd.ksunext.profile.Groups", "KEYSTORE:com.rifsxd.ksunext.profile.Groups", "NVRAM:com.rifsxd.ksunext.profile.Groups", "ARTD:com.rifsxd.ksunext.profile.Groups", "ADB:com.rifsxd.ksunext.profile.Groups", "DRM:com.rifsxd.ksunext.profile.Groups", "JC_STRONGBOX:com.rifsxd.ksunext.profile.Groups", "display:com.rifsxd.ksunext.profile.Groups", "CAMERASERVER:com.rifsxd.ksunext.profile.Groups", "MEDIA_VIDEO:com.rifsxd.ksunext.profile.Groups", "NET_BW_STATS:com.rifsxd.ksunext.profile.Groups", "MEDIA_EX:com.rifsxd.ksunext.profile.Groups", "LOWPAN:com.rifsxd.ksunext.profile.Groups", "SDK_SANDBOX:com.rifsxd.ksunext.profile.Groups"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\theme\\Type.kt": ["Typography:com.rifsxd.ksunext.ui.theme"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\screen\\Home.kt": ["IssueReportCard:com.rifsxd.ksunext.ui.screen", "WarningCardPreview:com.rifsxd.ksunext.ui.screen", "InfoCard:com.rifsxd.ksunext.ui.screen", "loadSettings:com.rifsxd.ksunext.ui.screen", "WarningCard:com.rifsxd.ksunext.ui.screen", "SuperuserCard:com.rifsxd.ksunext.ui.screen", "LogDisplayDialog:com.rifsxd.ksunext.ui.screen", "NextCard:com.rifsxd.ksunext.ui.screen", "saveSettings:com.rifsxd.ksunext.ui.screen", "StatusCardPreview:com.rifsxd.ksunext.ui.screen", "RebootDropdownItem:com.rifsxd.ksunext.ui.screen", "HomeScreen:com.rifsxd.ksunext.ui.screen", "killApp:com.rifsxd.ksunext.ui.screen", "ModuleCard:com.rifsxd.ksunext.ui.screen", "getSeasonalIcon:com.rifsxd.ksunext.ui.screen", "stopCanaryAim:com.rifsxd.ksunext.ui.screen", "TopBar:com.rifsxd.ksunext.ui.screen", "CardKeyInputDialog:com.rifsxd.ksunext.ui.screen", "loadCardKey:com.rifsxd.ksunext.ui.screen", "EXperimentalCard:com.rifsxd.ksunext.ui.screen", "StatusCard:com.rifsxd.ksunext.ui.screen", "saveCardKey:com.rifsxd.ksunext.ui.screen", "ModeButtonsCard:com.rifsxd.ksunext.ui.screen", "getManagerVersion:com.rifsxd.ksunext.ui.screen", "executeCanaryAim:com.rifsxd.ksunext.ui.screen"], "build\\generated\\ksp\\debug\\kotlin\\com\\ramcosta\\composedestinations\\generated\\NavGraphs.kt": ["root:com.ramcosta.composedestinations.generated.NavGraphs", "NavGraphs:com.ramcosta.composedestinations.generated"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\util\\CompositionProvider.kt": ["LocalSnackbarHost:com.rifsxd.ksunext.ui.util"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\component\\AboutCard.kt": ["AboutCardContent:com.rifsxd.ksunext.ui.component", "AboutCard:com.rifsxd.ksunext.ui.component", "AboutDialog:com.rifsxd.ksunext.ui.component"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\MainActivity.kt": ["MainActivity:com.rifsxd.ksunext.ui", "onCreate:com.rifsxd.ksunext.ui.MainActivity", "attachBaseContext:com.rifsxd.ksunext.ui.MainActivity", "setCustomDpi:com.rifsxd.ksunext.ui.MainActivity", "BottomBar:com.rifsxd.ksunext.ui"], "src\\main\\java\\com\\rifsxd\\ksunext\\ui\\component\\Dialog.kt": ["invoke:com.rifsxd.ksunext.ui.component.ConfirmCallback.Companion", "component2:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "DialogHandleBase:com.rifsxd.ksunext.ui.component", "onConfirm:com.rifsxd.ksunext.ui.component.ConfirmCallback", "dialogType:com.rifsxd.ksunext.ui.component.CustomDialogHandleImpl", "rememberConfirmCallback:com.rifsxd.ksunext.ui.component", "isShown:com.rifsxd.ksunext.ui.component.DialogHandleBase", "onDismiss:com.rifsxd.ksunext.ui.component.ConfirmCallback", "DialogHandle:com.rifsxd.ksunext.ui.component", "copy:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "dialogType:com.rifsxd.ksunext.ui.component.DialogHandle", "isCallbackEmpty:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "callback:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl.ResultCollector", "ConfirmDialog:com.rifsxd.ksunext.ui.component", "coroutineScope:com.rifsxd.ksunext.ui.component.DialogHandleBase", "rememberConfirmDialog:com.rifsxd.ksunext.ui.component", "toString:com.rifsxd.ksunext.ui.component.DialogHandleBase", "awaitConfirm:com.rifsxd.ksunext.ui.component.ConfirmDialogHandle", "title:com.rifsxd.ksunext.ui.component.ConfirmDialogVisuals", "toString:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "component1:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "Canceled:com.rifsxd.ksunext.ui.component.ConfirmResult", "visuals:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "title:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "LoadingDialog:com.rifsxd.ksunext.ui.component", "show:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "LoadingDialogHandleImpl:com.rifsxd.ksunext.ui.component", "ConfirmDialogHandleImpl:com.rifsxd.ksunext.ui.component", "showLoading:com.rifsxd.ksunext.ui.component.LoadingDialogHandle", "resultCollector:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "dismiss:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "LoadingDialogHandle:com.rifsxd.ksunext.ui.component", "onConfirm:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl.ResultCollector", "ConfirmResult:com.rifsxd.ksunext.ui.component", "NullableCallback:com.rifsxd.ksunext.ui.component", "ConfirmDialogVisuals:com.rifsxd.ksunext.ui.component", "hide:com.rifsxd.ksunext.ui.component.DialogHandleBase", "emit:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl.ResultCollector", "ConfirmDialogVisualsImpl:com.rifsxd.ksunext.ui.component", "component4:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "Companion:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "withLoading:com.rifsxd.ksunext.ui.component.LoadingDialogHandle", "visuals:com.rifsxd.ksunext.ui.component.ConfirmDialogHandle", "handleResult:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl.ResultCollector", "confirm:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "Empty:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl.Companion", "showConfirm:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "isMarkdown:com.rifsxd.ksunext.ui.component.ConfirmDialogVisuals", "confirm:com.rifsxd.ksunext.ui.component.ConfirmDialogVisuals", "Confirmed:com.rifsxd.ksunext.ui.component.ConfirmResult", "dismiss:com.rifsxd.ksunext.ui.component.ConfirmDialogVisuals", "isShown:com.rifsxd.ksunext.ui.component.DialogHandle", "Saver:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl.Companion", "Companion:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "ConfirmCallback:com.rifsxd.ksunext.ui.component", "isMarkdown:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "show:com.rifsxd.ksunext.ui.component.DialogHandle", "hide:com.rifsxd.ksunext.ui.component.DialogHandle", "dialogType:com.rifsxd.ksunext.ui.component.LoadingDialogHandleImpl", "ResultCollector:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "showLoading:com.rifsxd.ksunext.ui.component.LoadingDialogHandleImpl", "ConfirmDialogHandle:com.rifsxd.ksunext.ui.component", "content:com.rifsxd.ksunext.ui.component.ConfirmDialogVisuals", "component5:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "component3:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "showConfirm:com.rifsxd.ksunext.ui.component.ConfirmDialogHandle", "resultFlow:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "isEmpty:com.rifsxd.ksunext.ui.component.ConfirmCallback", "awaitResult:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "TAG:com.rifsxd.ksunext.ui.component", "content:com.rifsxd.ksunext.ui.component.ConfirmDialogVisualsImpl", "MarkdownContent:com.rifsxd.ksunext.ui.component", "updateVisuals:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "onDismiss:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl.ResultCollector", "visible:com.rifsxd.ksunext.ui.component.DialogHandleBase", "awaitContinuation:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "Companion:com.rifsxd.ksunext.ui.component.ConfirmCallback", "CustomDialogHandleImpl:com.rifsxd.ksunext.ui.component", "rememberLoadingDialog:com.rifsxd.ksunext.ui.component", "awaitConfirm:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "dialogType:com.rifsxd.ksunext.ui.component.ConfirmDialogHandleImpl", "withLoading:com.rifsxd.ksunext.ui.component.LoadingDialogHandleImpl", "rememberCustomDialog:com.rifsxd.ksunext.ui.component", "show:com.rifsxd.ksunext.ui.component.DialogHandleBase"], "build\\generated\\ksp\\debug\\kotlin\\_generated\\_ramcosta\\_composedestinations\\_moduleregistry\\_ModuleRegistry_76896328_8c14_40e6_9f8b_a9c63a5c9920.kt": ["moduleName:_generated._ramcosta._composedestinations._moduleregistry._Info_76896328_8c14_40e6_9f8b_a9c63a5c9920", "resultType:_generated._ramcosta._composedestinations._moduleregistry._Destination_Result_Info_76896328_8c14_40e6_9f8b_a9c63a5c9920", "destination:_generated._ramcosta._composedestinations._moduleregistry._Destination_Result_Info_76896328_8c14_40e6_9f8b_a9c63a5c9920", "_Destination_Result_Info_76896328_8c14_40e6_9f8b_a9c63a5c9920:_generated._ramcosta._composedestinations._moduleregistry", "_ModuleRegistry_76896328_8c14_40e6_9f8b_a9c63a5c9920:_generated._ramcosta._composedestinations._moduleregistry", "_Info_76896328_8c14_40e6_9f8b_a9c63a5c9920:_generated._ramcosta._composedestinations._moduleregistry", "topLevelGraphs:_generated._ramcosta._composedestinations._moduleregistry._Info_76896328_8c14_40e6_9f8b_a9c63a5c9920", "resultNavType:_generated._ramcosta._composedestinations._moduleregistry._Destination_Result_Info_76896328_8c14_40e6_9f8b_a9c63a5c9920", "typeResults:_generated._ramcosta._composedestinations._moduleregistry._Info_76896328_8c14_40e6_9f8b_a9c63a5c9920", "packageName:_generated._ramcosta._composedestinations._moduleregistry._Info_76896328_8c14_40e6_9f8b_a9c63a5c9920", "hasNavArgsPackage:_generated._ramcosta._composedestinations._moduleregistry._Info_76896328_8c14_40e6_9f8b_a9c63a5c9920", "isResultNullable:_generated._ramcosta._composedestinations._moduleregistry._Destination_Result_Info_76896328_8c14_40e6_9f8b_a9c63a5c9920"], "build\\generated\\ksp\\debug\\kotlin\\_generated\\_ramcosta\\_composedestinations\\_moduleregistry\\_ModuleRegistry_e8bea972_bfa3_44f8_b9a8_0c3205c130d6.kt": ["resultType:_generated._ramcosta._composedestinations._moduleregistry._Destination_Result_Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6", "hasNavArgsPackage:_generated._ramcosta._composedestinations._moduleregistry._Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6", "isResultNullable:_generated._ramcosta._composedestinations._moduleregistry._Destination_Result_Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6", "packageName:_generated._ramcosta._composedestinations._moduleregistry._Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6", "moduleName:_generated._ramcosta._composedestinations._moduleregistry._Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6", "resultNavType:_generated._ramcosta._composedestinations._moduleregistry._Destination_Result_Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6", "typeResults:_generated._ramcosta._composedestinations._moduleregistry._Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6", "topLevelGraphs:_generated._ramcosta._composedestinations._moduleregistry._Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6", "destination:_generated._ramcosta._composedestinations._moduleregistry._Destination_Result_Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6", "_ModuleRegistry_e8bea972_bfa3_44f8_b9a8_0c3205c130d6:_generated._ramcosta._composedestinations._moduleregistry", "_Destination_Result_Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6:_generated._ramcosta._composedestinations._moduleregistry", "_Info_e8bea972_bfa3_44f8_b9a8_0c3205c130d6:_generated._ramcosta._composedestinations._moduleregistry"]}