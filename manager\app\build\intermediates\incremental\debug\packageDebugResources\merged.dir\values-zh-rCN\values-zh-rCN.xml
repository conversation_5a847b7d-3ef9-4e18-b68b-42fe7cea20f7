<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="about">关于</string>
    <string name="about_source_code">在 %1$s 查看源代码</string>
    <string name="action">执行</string>
    <string name="allowlist_backup">备份超级用户列表</string>
    <string name="allowlist_backup_message">备份当前配置的超级用户列表。</string>
    <string name="allowlist_restore">恢复超级用户列表</string>
    <string name="allowlist_restore_message">从最近的备份中恢复超级用户列表。</string>
    <string name="app_name">Canary</string>
    <string name="app_profile_export_to_clipboard">导出到剪贴板</string>
    <string name="app_profile_import_export">导入 / 导出</string>
    <string name="app_profile_import_from_clipboard">从剪贴板导入</string>
    <string name="app_profile_template_create">新建模板</string>
    <string name="app_profile_template_delete">删除</string>
    <string name="app_profile_template_description">描述</string>
    <string name="app_profile_template_edit">编辑模板</string>
    <string name="app_profile_template_export_empty">没有本地模板可供导出！</string>
    <string name="app_profile_template_id">模板 ID</string>
    <string name="app_profile_template_id_exist">模版 ID 已存在！</string>
    <string name="app_profile_template_id_invalid">模版 ID 不合法</string>
    <string name="app_profile_template_import_empty">剪贴板为空！</string>
    <string name="app_profile_template_import_success">导入成功</string>
    <string name="app_profile_template_name">名称</string>
    <string name="app_profile_template_readonly">只读</string>
    <string name="app_profile_template_save">保存</string>
    <string name="app_profile_template_save_failed">模版保存失败</string>
    <string name="app_profile_template_sync">同步在线模板</string>
    <string name="app_profile_template_view">查看模板</string>
    <string name="backup_restore">备份 &amp; 恢复</string>
    <string name="cancel">取消</string>
    <string name="close">关闭</string>
    <string name="confirm">确认</string>
    <string name="customization">界面设置</string>
    <string name="developer">开发者选项</string>
    <string name="direct_install">直接安装（推荐）</string>
    <string name="disable">禁用</string>
    <string name="disabled">已禁用</string>
    <string name="enable">启用</string>
    <string name="enable_developer_options">启用开发者选项</string>
    <string name="enable_developer_options_summary">显示仅适合开发者的隐藏设置和调试信息。</string>
    <string name="enable_web_debugging">启用 WebView 调试</string>
    <string name="enable_web_debugging_summary">可用于调试 WebUI，请仅在需要时启用。</string>
    <string name="enabled">已启用</string>
    <string name="export_log">导出日志</string>
    <string name="failed_to_update_app_profile">为 %s 更新应用配置失败</string>
    <string name="failed_to_update_sepolicy">未能更新 %s 的 SELinux 规则</string>
    <string name="flash_failed">刷写失败</string>
    <string name="flash_success">刷写成功</string>
    <string name="flashing">刷写中</string>
    <string name="force_stop_app">强行停止</string>
    <string name="grant_root_failed">获取 root 失败！</string>
    <string name="hide_system_apps">隐藏系统应用</string>
    <string name="home">首页</string>
    <string name="home_abi">接口</string>
    <string name="home_android">Android 版本</string>
    <string name="home_click_to_install">请授权应用Root</string>
    <string name="home_experimental_kernelsu">⚠️ 实验性开发警告！</string>
    <string name="home_experimental_kernelsu_body">KernelSU Next 是一个非官方版本，一直处于积极的实验开发阶段。它按原样提供，不保证稳定性、性能或可靠性。</string>
    <string name="home_experimental_kernelsu_body_point_1"> • 风险自担：可能会出现崩溃、意外行为或导致系统故障。</string>
    <string name="home_experimental_kernelsu_body_point_2"> • 不做保证：开发者不对数据丢失、系统损坏等问题负责。</string>
    <string name="home_experimental_kernelsu_body_point_3"> • 仅供测试：此版适合了解风险并能轻松解决问题的用户。</string>
    <string name="home_experimental_kernelsu_repo">127.0.0.1</string>
    <string name="home_failure">无法在内核中找到 KernelSU Next v2 签名！[ !KSU_NEXT || != size/hash ]</string>
    <string name="home_failure_tip">请让你的内核开发人员集成 KernelSU Next！</string>
    <string name="home_kernel">内核版本</string>
    <string name="home_magic_mount">Magic Mount</string>
    <string name="home_manager_version">管理器版本</string>
    <string name="home_module_count_plural">模块</string>
    <string name="home_module_count_singular">模块</string>
    <string name="home_module_update_count">%d 个模块可更新！</string>
    <string name="home_mount_system">模块系统</string>
    <string name="home_next_kernelsu">🔥 Next 构建</string>
    <string name="home_next_kernelsu_body">Next 实验性分支。在 GitHub 上查看！</string>
    <string name="home_next_kernelsu_repo">https://github.com/KernelSU-Next/KernelSU-Next</string>
    <string name="home_not_installed">未获取Root</string>
    <string name="home_overlayfs_mount">OverlayFS</string>
    <string name="home_selinux_status">SELinux 状态</string>
    <string name="home_superuser_count_plural">超级用户</string>
    <string name="home_superuser_count_singular">超级用户</string>
    <string name="home_susfs">SuSFS：%s</string>
    <string name="home_susfs_sus_su">SuS SU</string>
    <string name="home_susfs_version">SuSFS 版本</string>
    <string name="home_working">工作中</string>
    <string name="home_working_version">版本：%d</string>
    <string name="hook_mode">Hook 模式</string>
    <string name="install">安装</string>
    <string name="install_inactive_slot">安装到未使用的槽位（OTA 后）</string>
    <string name="install_inactive_slot_warning">将在重启后 **强制** 切换到另一个槽位！\n注意只能在 OTA 更新完成后的重启之前使用。\n确认？</string>
    <string name="install_next">下一步</string>
    <string name="issue_report_body">发现错误或者有改进建议？</string>
    <string name="issue_report_body_2">快向我们报告吧！</string>
    <string name="issue_report_github">去 GitHub 报告</string>
    <string name="issue_report_github_link">https://github.com/KernelSU-Next/KernelSU-Next/issues</string>
    <string name="issue_report_telegram">在 Telegram 获得最新消息</string>
    <string name="issue_report_telegram_link">https://t.me/ksunext</string>
    <string name="issue_report_title">需要帮助？</string>
    <string name="later">稍后</string>
    <string name="launch_app">启动</string>
    <string name="lkm_alternative_suggestion">安装 GKI 内核或集成 KernelSU 到你的设备。</string>
    <string name="lkm_mode_deprecated">LKM 模式已弃用！</string>
    <string name="lkm_warning_message">此 LKM 补丁依赖于闭源组件，确认操作代表你知晓因继续使用该功能所导致的一切后果与开发团队无关。</string>
    <string name="log_saved">日志已保存</string>
    <string name="module">模块</string>
    <string name="module_author">作者</string>
    <string name="module_backup">备份模块</string>
    <string name="module_backup_message">备份当前已安装的模块。</string>
    <string name="module_changelog">更新日志</string>
    <string name="module_changelog_failed">获取更新日志失败：%s</string>
    <string name="module_downloading">正在下载模块：%s</string>
    <string name="module_empty">未安装任何模块</string>
    <string name="module_failed_to_disable">禁用模块失败：%s</string>
    <string name="module_failed_to_enable">启用模块失败：%s</string>
    <string name="module_id">模块标识</string>
    <string name="module_install">安装</string>
    <string name="module_install_prompt_with_name">将安装以下模块：%1$s</string>
    <string name="module_magisk_conflict">由于与 Magisk 冲突，模块系统不可用！</string>
    <string name="module_overlay_fs_not_available">OverlayFS 被内核禁用，模块系统不可用！</string>
    <string name="module_restore">恢复模块</string>
    <string name="module_restore_confirm">确定要还原模块 %s 吗？</string>
    <string name="module_restore_failed">恢复失败：%s</string>
    <string name="module_restore_message">从最近的备份中恢复模块。</string>
    <string name="module_restore_success">已还原 %s</string>
    <string name="module_size_high_to_low">按模块大小倒序排序</string>
    <string name="module_size_low_to_high">按模块大小正序排序</string>
    <string name="module_sort_a_to_z">按 A - Z 排序</string>
    <string name="module_sort_action_first">按支持执行脚本的模块优先排序</string>
    <string name="module_sort_enabled_first">按启用状态优先排序</string>
    <string name="module_sort_webui_first">按拥有 WebUI 的模块优先排序</string>
    <string name="module_sort_z_to_a">按 Z - A 排序</string>
    <string name="module_start_downloading">开始下载：%s</string>
    <string name="module_uninstall_confirm">确定要卸载模块 %s 吗？</string>
    <string name="module_uninstall_failed">卸载失败：%s</string>
    <string name="module_uninstall_success">已卸载 %s</string>
    <string name="module_update">更新</string>
    <string name="module_update_available">更新可用</string>
    <string name="module_update_json">更新配置</string>
    <string name="module_update_json_empty">无更新配置</string>
    <string name="module_updated">重启以应用更新</string>
    <string name="module_version">版本</string>
    <string name="module_version_code">模块版本</string>
    <string name="new_version_available">发现新版本：%s，点击升级。</string>
    <string name="open">打开</string>
    <string name="proceed">继续</string>
    <string name="profile">应用配置</string>
    <string name="profile_capabilities">权能</string>
    <string name="profile_custom">自定义</string>
    <string name="profile_default">默认</string>
    <string name="profile_groups">组</string>
    <string name="profile_name">名称</string>
    <string name="profile_namespace">挂载命名空间</string>
    <string name="profile_namespace_global">全局</string>
    <string name="profile_namespace_individual">私有</string>
    <string name="profile_namespace_inherited">继承</string>
    <string name="profile_selinux_context">SELinux</string>
    <string name="profile_selinux_domain">域</string>
    <string name="profile_selinux_rules">规则</string>
    <string name="profile_template">模板</string>
    <string name="profile_umount_modules">卸载模块</string>
    <string name="profile_umount_modules_summary">启用后将允许 KernelSU Next 为本应用还原被模块修改过的文件。</string>
    <string name="reboot">重启</string>
    <string name="reboot_bootloader">重启到引导加载程序</string>
    <string name="reboot_download">重启到下载模式</string>
    <string name="reboot_edl">重启到 EDL 模式</string>
    <string name="reboot_message">更改将在系统重启后生效。现在重启？</string>
    <string name="reboot_recovery">重启到恢复模式</string>
    <string name="reboot_required">需要重启</string>
    <string name="reboot_to_apply">重启生效</string>
    <string name="reboot_userspace">软重启</string>
    <string name="refresh">刷新</string>
    <string name="require_kernel_version">当前 KernelSU Next 版本 %1$d 过低，管理器无法正常工作，请升级内核 KernelSU Next 版本至 %2$d 或以上！</string>
    <string name="restart_app">重新启动</string>
    <string name="restart_app_message">请重启 APP 以确保更改生效。</string>
    <string name="restart_required">需要重启</string>
    <string name="restore">还原</string>
    <string name="safe_mode">安全模式</string>
    <string name="save_log">保存日志</string>
    <string name="select_file">选择一个文件</string>
    <string name="select_file_tip">建议选择 %1$s 分区镜像</string>
    <string name="select_kmi">选择 KMI</string>
    <string name="selected_lkm">选择的 LKM：%s</string>
    <string name="selinux_status_disabled">禁用</string>
    <string name="selinux_status_enforcing">严格模式</string>
    <string name="selinux_status_permissive">宽容模式</string>
    <string name="selinux_status_unknown">未知</string>
    <string name="send_log">分享日志</string>
    <string name="settings">设置</string>
    <string name="settings_amoled_mode">AMOLED 模式</string>
    <string name="settings_amoled_mode_summary">启用适用于 AMOLED 屏幕的纯黑主题，可减少视觉疲劳并节省电量。</string>
    <string name="settings_banner">启用横幅</string>
    <string name="settings_banner_summary">显示模块的背景横幅（如有提供）。</string>
    <string name="settings_check_update">检查更新</string>
    <string name="settings_check_update_summary">在应用启动后自动检查是否有新版本。</string>
    <string name="settings_disable_su">禁用 SU 兼容</string>
    <string name="settings_disable_su_summary">临时禁止任何应用通过 su 命令获得 root 权限（正在运行的 root 进程不受影响）。</string>
    <string name="settings_language">语言</string>
    <string name="settings_legacyui">使用旧界面</string>
    <string name="settings_legacyui_summary">切换到早期用户界面风格。</string>
    <string name="settings_profile_template">应用配置模版</string>
    <string name="settings_profile_template_summary">管理本地和在线的应用配置模版。</string>
    <string name="settings_restore_stock_image">恢复原厂镜像</string>
    <string name="settings_restore_stock_image_message">恢复原厂镜像（若存在备份），一般在 OTA 前使用；如需卸载请使用“完全卸载”。</string>
    <string name="settings_susfs_toggle">隐藏 kprobes 钩子</string>
    <string name="settings_susfs_toggle_summary">禁用由 KSU 创建的 Kprobes 钩子，并用非 Kprobes 的内联钩子代替，实现方式类似于不支持 Kprobe 的非 GKI 内核。</string>
    <string name="settings_umount_modules_default">默认卸载模块</string>
    <string name="settings_umount_modules_default_summary">应用配置中“卸载模块”的全局默认值。如果启用，将会为没有设置应用配置的应用移除所有模块针对系统的修改。</string>
    <string name="settings_uninstall">卸载</string>
    <string name="settings_uninstall_permanent">永久卸载</string>
    <string name="settings_uninstall_permanent_message">彻底移除 KernelSU Next，将丢失 Root 权限及所有已安装的模块。</string>
    <string name="settings_uninstall_temporary">临时卸载</string>
    <string name="settings_uninstall_temporary_message">临时卸载 KernelSU Next，下次重启后恢复。</string>
    <string name="show_system_apps">显示系统应用</string>
    <string name="shrink_sparse_image">最小化稀疏文件</string>
    <string name="shrink_sparse_image_message">将模块所在的稀疏文件镜像调整为其实际大小，注意这可能导致模块工作异常，所以请仅在必要时（如备份）使用。</string>
    <string name="su_not_allowed">拒绝授予 %s 超级用户权限</string>
    <string name="sucompat_disabled">SU 兼容被禁用</string>
    <string name="superuser">超级用户</string>
    <string name="susfs_supported">支持</string>
    <string name="system_default">系统默认</string>
    <string name="unavailable">不可用</string>
    <string name="uninstall">卸载</string>
    <string name="uninstalled">重启以应用卸载</string>
    <string name="use_overlay_fs">使用 OverlayFS</string>
    <string name="use_overlay_fs_summary">使用 OverlayFS 而非 Magic Mount 作为 KernelSU Next 的挂载系统。</string>
    <string name="use_webuix">使用 WebUI X</string>
    <string name="use_webuix_eruda">将 Eruda 注入到 WebUI X</string>
    <string name="use_webuix_eruda_summary">将调试控制台注入 WebUI X 以使调试更容易。需要启用 Web 调试。</string>
    <string name="use_webuix_summary">使用支持更多 API 的 WebUI X 而非 WebUI。</string>
    <string name="warning">免责声明</string>
    <string name="warning_message">此为尚在开发阶段的实验功能，继续操作前请确保模块已备份。使用此功能需要先了解其风险，在知晓后果的情况下谨慎操作。</string>
    <string name="webui">WebUI</string>
    <string name="zygisk_required">需要 Zygisk</string>
    <string name="zygisk_status">Zygisk 注入</string>
</resources>