<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.github.topjohnwu.libsu:service:6.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0aa38518544eca4718361df7fd0b0571\transformed\service-6.0.0\assets"><file name="main.jar" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0aa38518544eca4718361df7fd0b0571\transformed\service-6.0.0\assets\main.jar"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\assets"><file name="eruda.min.js" path="C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\assets\eruda.min.js"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>