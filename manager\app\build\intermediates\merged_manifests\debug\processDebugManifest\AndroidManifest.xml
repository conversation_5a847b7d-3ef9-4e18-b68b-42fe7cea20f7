<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.rifsxd.ksunext"
    android:versionCode="10700"
    android:versionName="v1.0.9" >

    <uses-sdk
        android:minSdkVersion="26"
        android:targetSdkVersion="36" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <permission
        android:name="com.rifsxd.ksunext.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.rifsxd.ksunext.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.rifsxd.ksunext.KernelSUApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:enableOnBackInvokedCallback="true"
        android:extractNativeLibs="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:localeConfig="@xml/_generated_res_locale_config"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/Theme.KernelSU" >
        <activity
            android:name="com.rifsxd.ksunext.ui.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.KernelSU" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:mimeType="application/zip" />
                <data android:scheme="file" />
                <data android:scheme="content" />
                <data android:pathPattern=".*\\.zip" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.rifsxd.ksunext.ui.webui.WebUIActivity"
            android:autoRemoveFromRecents="true"
            android:documentLaunchMode="intoExisting"
            android:exported="false"
            android:theme="@style/Theme.KernelSU.WebUI" />
        <activity
            android:name="com.rifsxd.ksunext.ui.webui.WebUIXActivity"
            android:autoRemoveFromRecents="true"
            android:documentLaunchMode="intoExisting"
            android:exported="false"
            android:theme="@style/Theme.KernelSU.WebUI" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.rifsxd.ksunext.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider>

        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.rifsxd.ksunext.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Material.Light.NoActionBar" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>