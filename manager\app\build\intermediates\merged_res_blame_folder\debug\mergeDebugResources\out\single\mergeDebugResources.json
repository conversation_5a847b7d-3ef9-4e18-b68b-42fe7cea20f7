[{"merged": "com.rifsxd.ksunext.app-debug-79:/drawable_ic_telegram.xml.flat", "source": "com.rifsxd.ksunext.app-main-81:/drawable/ic_telegram.xml"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/xml_network_security_config.xml.flat", "source": "com.rifsxd.ksunext.app-main-81:/xml/network_security_config.xml"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-xhdpi_ic_launcher_monochrome.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-xhdpi/ic_launcher_monochrome.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/drawable_ic_sus.xml.flat", "source": "com.rifsxd.ksunext.app-main-81:/drawable/ic_sus.xml"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/xml_filepaths.xml.flat", "source": "com.rifsxd.ksunext.app-main-81:/xml/filepaths.xml"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/xml__generated_res_locale_config.xml.flat", "source": "com.rifsxd.ksunext.app-localeConfig-74:/xml/_generated_res_locale_config.xml"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/drawable_ic_linux.xml.flat", "source": "com.rifsxd.ksunext.app-main-81:/drawable/ic_linux.xml"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-mdpi_ic_launcher_monochrome.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-mdpi/ic_launcher_monochrome.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/drawable_ic_ksu_next.xml.flat", "source": "com.rifsxd.ksunext.app-main-81:/drawable/ic_ksu_next.xml"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-hdpi_ic_launcher_monochrome.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-hdpi/ic_launcher_monochrome.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-xxhdpi_ic_launcher_monochrome.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-xxhdpi/ic_launcher_monochrome.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/xml_backup_rules.xml.flat", "source": "com.rifsxd.ksunext.app-main-81:/xml/backup_rules.xml"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-xxxhdpi_ic_launcher_foreground.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-xxxhdpi/ic_launcher_foreground.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/drawable_ic_github.xml.flat", "source": "com.rifsxd.ksunext.app-main-81:/drawable/ic_github.xml"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-mdpi_ic_launcher_foreground.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-mdpi/ic_launcher_foreground.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-xxhdpi_ic_launcher_foreground.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-xxhdpi/ic_launcher_foreground.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/xml_data_extraction_rules.xml.flat", "source": "com.rifsxd.ksunext.app-main-81:/xml/data_extraction_rules.xml"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-hdpi_ic_launcher_foreground.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-hdpi/ic_launcher_foreground.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-xxxhdpi_ic_launcher_monochrome.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-xxxhdpi/ic_launcher_monochrome.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-xhdpi_ic_launcher_foreground.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-xhdpi/ic_launcher_foreground.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.rifsxd.ksunext.app-debug-79:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.rifsxd.ksunext.app-main-81:/mipmap-anydpi/ic_launcher.xml"}]