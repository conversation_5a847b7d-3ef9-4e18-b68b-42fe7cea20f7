1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.rifsxd.ksunext"
4    android:versionCode="10700"
5    android:versionName="v1.0.9" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
12-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:6:5-76
12-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:6:22-74
13
14    <permission
14-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.rifsxd.ksunext.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.rifsxd.ksunext.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:8:5-62:19
21        android:name="com.rifsxd.ksunext.KernelSUApplication"
21-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:9:9-44
22        android:allowBackup="true"
22-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:enableOnBackInvokedCallback="true"
26-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:12:9-51
27        android:extractNativeLibs="true"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:15:9-41
31        android:localeConfig="@xml/_generated_res_locale_config"
32        android:networkSecurityConfig="@xml/network_security_config"
32-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:16:9-69
33        android:supportsRtl="true"
33-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:17:9-35
34        android:theme="@style/Theme.KernelSU" >
34-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:18:9-46
35        <activity
35-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:20:9-37:20
36            android:name="com.rifsxd.ksunext.ui.MainActivity"
36-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:21:13-44
37            android:exported="true"
37-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:22:13-36
38            android:theme="@style/Theme.KernelSU" >
38-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:23:13-50
39            <intent-filter>
39-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:24:13-27:29
40                <action android:name="android.intent.action.MAIN" />
40-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:25:17-69
40-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:25:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:26:17-77
42-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:26:27-74
43            </intent-filter>
44            <intent-filter>
44-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:28:13-36:29
45                <action android:name="android.intent.action.VIEW" />
45-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:29:17-69
45-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:29:25-66
46
47                <category android:name="android.intent.category.DEFAULT" />
47-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:30:17-76
47-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:30:27-73
48                <category android:name="android.intent.category.BROWSABLE" />
48-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:31:17-78
48-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:31:27-75
49
50                <data android:mimeType="application/zip" />
50-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:32:17-60
50-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:32:23-57
51                <data android:scheme="file" />
51-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:32:17-60
51-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:33:23-44
52                <data android:scheme="content" />
52-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:32:17-60
52-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:33:23-44
53                <data android:pathPattern=".*\\.zip" />
53-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:32:17-60
53-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:35:23-53
54            </intent-filter>
55        </activity>
56        <activity
56-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:39:9-44:59
57            android:name="com.rifsxd.ksunext.ui.webui.WebUIActivity"
57-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:40:13-51
58            android:autoRemoveFromRecents="true"
58-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:41:13-49
59            android:documentLaunchMode="intoExisting"
59-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:42:13-54
60            android:exported="false"
60-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:43:13-37
61            android:theme="@style/Theme.KernelSU.WebUI" />
61-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:44:13-56
62        <activity
62-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:46:9-51:59
63            android:name="com.rifsxd.ksunext.ui.webui.WebUIXActivity"
63-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:47:13-52
64            android:autoRemoveFromRecents="true"
64-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:48:13-49
65            android:documentLaunchMode="intoExisting"
65-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:49:13-54
66            android:exported="false"
66-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:50:13-37
67            android:theme="@style/Theme.KernelSU.WebUI" />
67-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:51:13-56
68
69        <provider
70            android:name="androidx.core.content.FileProvider"
70-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:54:13-62
71            android:authorities="com.rifsxd.ksunext.fileprovider"
71-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:55:13-64
72            android:exported="false"
72-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:56:13-37
73            android:grantUriPermissions="true" >
73-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:57:13-47
74            <meta-data
74-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:58:13-60:53
75                android:name="android.support.FILE_PROVIDER_PATHS"
75-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:59:17-67
76                android:resource="@xml/filepaths" />
76-->C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:60:17-50
77        </provider>
78
79        <activity
79-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
80            android:name="androidx.compose.ui.tooling.PreviewActivity"
80-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
81            android:exported="true" />
81-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
82
83        <provider
83-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
84            android:name="androidx.startup.InitializationProvider"
84-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
85            android:authorities="com.rifsxd.ksunext.androidx-startup"
85-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
86            android:exported="false" >
86-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
87            <meta-data
87-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
88                android:name="androidx.emoji2.text.EmojiCompatInitializer"
88-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
89                android:value="androidx.startup" />
89-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
90            <meta-data
90-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
91                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
91-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
92                android:value="androidx.startup" />
92-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
93            <meta-data
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
94                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
95                android:value="androidx.startup" />
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
96        </provider>
97
98        <activity
98-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:23:9-26:79
99            android:name="androidx.activity.ComponentActivity"
99-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:24:13-63
100            android:exported="true"
100-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:25:13-36
101            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
101-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:26:13-76
102
103        <receiver
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
104            android:name="androidx.profileinstaller.ProfileInstallReceiver"
104-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
105            android:directBootAware="false"
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
106            android:enabled="true"
106-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
107            android:exported="true"
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
108            android:permission="android.permission.DUMP" >
108-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
110                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
113                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
116                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
119                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
119-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
119-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
120            </intent-filter>
121        </receiver>
122    </application>
123
124</manifest>
