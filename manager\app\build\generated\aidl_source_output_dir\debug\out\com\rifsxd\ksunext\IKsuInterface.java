/*
 * This file is auto-generated.  DO NOT MODIFY.
 * Using: C:\Android\SDK\build-tools\35.0.0\aidl.exe -pC:\Android\SDK\platforms\android-36\framework.aidl -oC:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\build\generated\aidl_source_output_dir\debug\out -IC:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\aidl -IC:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\debug\aidl -IC:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\aidl -IC:\Users\<USER>\.gradle\caches\8.14.2\transforms\48c9d3c93c0ff39da09afcf242af84ce\transformed\versionedparcelable-1.1.1\aidl -IC:\Users\<USER>\.gradle\caches\8.14.2\transforms\538d41cd23daafbb8879ca4f42f2f664\transformed\parcelablelist-2.0.1-debug\aidl -dC:\Users\<USER>\AppData\Local\Temp\aidl5956343085109860724.d C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\aidl\com\rifsxd\ksunext\IKsuInterface.aidl
 */
package com.rifsxd.ksunext;
public interface IKsuInterface extends android.os.IInterface
{
  /** Default implementation for IKsuInterface. */
  public static class Default implements com.rifsxd.ksunext.IKsuInterface
  {
    @Override public rikka.parcelablelist.ParcelableListSlice<android.content.pm.PackageInfo> getPackages(int flags) throws android.os.RemoteException
    {
      return null;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.rifsxd.ksunext.IKsuInterface
  {
    /** Construct the stub at attach it to the interface. */
    @SuppressWarnings("this-escape")
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.rifsxd.ksunext.IKsuInterface interface,
     * generating a proxy if needed.
     */
    public static com.rifsxd.ksunext.IKsuInterface asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.rifsxd.ksunext.IKsuInterface))) {
        return ((com.rifsxd.ksunext.IKsuInterface)iin);
      }
      return new com.rifsxd.ksunext.IKsuInterface.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      if (code >= android.os.IBinder.FIRST_CALL_TRANSACTION && code <= android.os.IBinder.LAST_CALL_TRANSACTION) {
        data.enforceInterface(descriptor);
      }
      if (code == INTERFACE_TRANSACTION) {
        reply.writeString(descriptor);
        return true;
      }
      switch (code)
      {
        case TRANSACTION_getPackages:
        {
          int _arg0;
          _arg0 = data.readInt();
          rikka.parcelablelist.ParcelableListSlice<android.content.pm.PackageInfo> _result = this.getPackages(_arg0);
          reply.writeNoException();
          _Parcel.writeTypedObject(reply, _result, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
          break;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
      return true;
    }
    private static class Proxy implements com.rifsxd.ksunext.IKsuInterface
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public rikka.parcelablelist.ParcelableListSlice<android.content.pm.PackageInfo> getPackages(int flags) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        rikka.parcelablelist.ParcelableListSlice<android.content.pm.PackageInfo> _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(flags);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getPackages, _data, _reply, 0);
          _reply.readException();
          _result = _Parcel.readTypedObject(_reply, rikka.parcelablelist.ParcelableListSlice.CREATOR);
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
    }
    static final int TRANSACTION_getPackages = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
  }
  /** @hide */
  public static final java.lang.String DESCRIPTOR = "com.rifsxd.ksunext.IKsuInterface";
  public rikka.parcelablelist.ParcelableListSlice<android.content.pm.PackageInfo> getPackages(int flags) throws android.os.RemoteException;
  /** @hide */
  static class _Parcel {
    static private <T> T readTypedObject(
        android.os.Parcel parcel,
        android.os.Parcelable.Creator<T> c) {
      if (parcel.readInt() != 0) {
          return c.createFromParcel(parcel);
      } else {
          return null;
      }
    }
    static private <T extends android.os.Parcelable> void writeTypedObject(
        android.os.Parcel parcel, T value, int parcelableFlags) {
      if (value != null) {
        parcel.writeInt(1);
        value.writeToParcel(parcel, parcelableFlags);
      } else {
        parcel.writeInt(0);
      }
    }
  }
}
