package _generated._ramcosta._composedestinations._moduleregistry

import com.ramcosta.composedestinations.spec.DestinationSpec

public annotation class _Info_0ad6136c_21b6_4891_81f9_92ddd106d393(
    val moduleName: String,
    val packageName: String,
    val hasNavArgsPackage: Boolean,
    val typeResults: Array<_Destination_Result_Info_0ad6136c_21b6_4891_81f9_92ddd106d393> = emptyArray(),
    val topLevelGraphs: Array<String> = emptyArray()
)

public annotation class _Destination_Result_Info_0ad6136c_21b6_4891_81f9_92ddd106d393(
    val destination: String,
    val resultType: String,
    val resultNavType: String,
    val isResultNullable: Boolean
)

@_Info_0ad6136c_21b6_4891_81f9_92ddd106d393(
    moduleName = "",
    packageName = "com.ramcosta.composedestinations.generated",
    hasNavArgsPackage = false,
    typeResults = [

    ],
    topLevelGraphs = [
		"RootNavGraph"
    ]
)
public object _ModuleRegistry_0ad6136c_21b6_4891_81f9_92ddd106d393