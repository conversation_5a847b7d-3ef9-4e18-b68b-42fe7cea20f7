package _generated._ramcosta._composedestinations._moduleregistry

import com.ramcosta.composedestinations.spec.DestinationSpec

public annotation class _Info_d4a47f6b_7af6_4d9f_8ec5_45d054b53963(
    val moduleName: String,
    val packageName: String,
    val hasNavArgsPackage: Boolean,
    val typeResults: Array<_Destination_Result_Info_d4a47f6b_7af6_4d9f_8ec5_45d054b53963> = emptyArray(),
    val topLevelGraphs: Array<String> = emptyArray()
)

public annotation class _Destination_Result_Info_d4a47f6b_7af6_4d9f_8ec5_45d054b53963(
    val destination: String,
    val resultType: String,
    val resultNavType: String,
    val isResultNullable: Boolean
)

@_Info_d4a47f6b_7af6_4d9f_8ec5_45d054b53963(
    moduleName = "",
    packageName = "com.ramcosta.composedestinations.generated",
    hasNavArgsPackage = false,
    typeResults = [

    ],
    topLevelGraphs = [
		"RootNavGraph"
    ]
)
public object _ModuleRegistry_d4a47f6b_7af6_4d9f_8ec5_45d054b53963