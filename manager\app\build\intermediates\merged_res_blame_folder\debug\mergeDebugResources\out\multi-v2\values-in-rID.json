{"logs": [{"outputFile": "com.rifsxd.ksunext.app-mergeDebugResources-77:/values-in-rID/values-in-rID.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\4a70d17a9618e65af9cd54de947c82cb\\transformed\\list-1.3.0\\res\\values-in-rID\\values-in-rID.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,103", "endOffsets": "154,258"}, "to": {"startLines": "4,5", "startColumns": "4,4", "startOffsets": "130,234", "endColumns": "103,103", "endOffsets": "229,333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\a64fe05c0979e98a37ab829c2d67ec24\\transformed\\core-1.3.0\\res\\values-in-rID\\values-in-rID.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,96", "endColumns": "40,33", "endOffsets": "91,125"}}]}]}