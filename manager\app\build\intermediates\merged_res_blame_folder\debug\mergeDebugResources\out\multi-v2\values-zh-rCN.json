{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.rifsxd.ksunext.app-mergeDebugResources-77:\\values-zh-rCN\\values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\baf1b711915162474bbf875d9a8baf28\\transformed\\foundation-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,207", "endColumns": "70,80,76", "endOffsets": "121,202,279"}, "to": {"startLines": "60,419,420", "startColumns": "4,4,4", "startOffsets": "4493,29690,29771", "endColumns": "70,80,76", "endOffsets": "4559,29766,29843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ed59826fbda97ece0491b02ad2a5c49d\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,254,330,415,506,583,657,734,812,887,960,1035,1103,1184,1257,1329,1400,1473,1539", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "249,325,410,501,578,652,729,807,882,955,1030,1098,1179,1252,1324,1395,1468,1534,1650"}, "to": {"startLines": "79,80,84,85,90,146,147,320,322,343,344,370,406,407,408,409,415,417,418", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5971,6048,6293,6378,6645,10229,10303,23406,23549,24674,24747,26281,28786,28867,28940,29012,29387,29508,29574", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "6043,6119,6373,6464,6717,10298,10375,23479,23619,24742,24817,26344,28862,28935,29007,29078,29455,29569,29685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\a64fe05c0979e98a37ab829c2d67ec24\\transformed\\core-1.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "93", "endColumns": "34", "endOffsets": "123"}, "to": {"startLines": "323", "startColumns": "4", "startOffsets": "23624", "endColumns": "34", "endOffsets": "23654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ea4b31e0f2565dabf3acdd6c35348c38\\transformed\\material-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,911,973,1050,1109,1168,1246,1307,1364,1420,1479,1537,1591,1676,1732,1790,1844,1909,2001,2075,2147,2226,2300,2376,2498,2560,2622,2721,2800,2874,2924,2975,3041,3105,3174,3245,3316,3377,3448,3515,3575,3661,3740,3807,3890,3975,4049,4114,4190,4238,4311,4375,4451,4529,4591,4655,4718,4783,4863,4939,5017,5093,5147,5202,5271,5346,5419", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "242,306,368,438,508,585,676,782,855,906,968,1045,1104,1163,1241,1302,1359,1415,1474,1532,1586,1671,1727,1785,1839,1904,1996,2070,2142,2221,2295,2371,2493,2555,2617,2716,2795,2869,2919,2970,3036,3100,3169,3240,3311,3372,3443,3510,3570,3656,3735,3802,3885,3970,4044,4109,4185,4233,4306,4370,4446,4524,4586,4650,4713,4778,4858,4934,5012,5088,5142,5197,5266,5341,5414,5484"}, "to": {"startLines": "2,62,63,64,65,66,75,76,77,97,98,100,145,159,223,224,225,226,227,228,229,230,231,232,233,234,235,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,325,365,366,405", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,4619,4683,4745,4815,4885,5664,5755,5861,7084,7135,7241,10170,11122,17083,17161,17222,17279,17335,17394,17452,17506,17591,17647,17705,17759,17824,20205,20279,20351,20430,20504,20580,20702,20764,20826,20925,21004,21078,21128,21179,21245,21309,21378,21449,21520,21581,21652,21719,21779,21865,21944,22011,22094,22179,22253,22318,22394,22442,22515,22579,22655,22733,22795,22859,22922,22987,23067,23143,23221,23297,23351,23695,25981,26056,28716", "endLines": "5,62,63,64,65,66,75,76,77,97,98,100,145,159,223,224,225,226,227,228,229,230,231,232,233,234,235,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,325,365,366,405", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "292,4678,4740,4810,4880,4957,5750,5856,5929,7130,7192,7313,10224,11176,17156,17217,17274,17330,17389,17447,17501,17586,17642,17700,17754,17819,17911,20274,20346,20425,20499,20575,20697,20759,20821,20920,20999,21073,21123,21174,21240,21304,21373,21444,21515,21576,21647,21714,21774,21860,21939,22006,22089,22174,22248,22313,22389,22437,22510,22574,22650,22728,22790,22854,22917,22982,23062,23138,23216,23292,23346,23401,23759,26051,26124,28781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\b0c018149e18020465f8106101906127\\transformed\\browser-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "82,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "6163,7464,7556,7657", "endColumns": "82,91,100,92", "endOffsets": "6241,7551,7652,7745"}}, {"source": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\src\\main\\res\\values-zh-rCN\\strings.xml", "from": {"startLines": "61,115,180,98,99,96,97,10,170,168,169,157,164,162,158,171,159,167,160,175,172,161,166,163,174,173,165,94,103,145,9,220,221,185,24,26,23,74,75,183,184,25,80,129,152,203,202,201,146,179,79,11,33,31,13,109,111,112,113,114,110,19,20,21,85,32,228,227,18,84,106,108,107,12,86,34,226,225,28,30,29,16,17,22,52,187,188,189,3,4,5,7,6,8,2,104,144,15,14,105,206,43,69,95,229,154,176,141,42,41,40,70,51,44,83,76,92,65,67,93,66,48,47,45,231,230,232,46,142,62,64,63,138,139,72,73,140,68,71,143,182,102,116,126,119,117,125,120,121,123,124,122,127,136,137,118,128,135,53,58,59,60,91,57,90,82,56,77,130,147,151,150,50,81,205,186,190,191,204,35,36,37,38,207,55,148,149,214,215,177,178,208,209,210,212,213,155,156,197,200,133,134,131,132,194,196,199,195,198,78,192,193,153,222,39,27,211,87,49,54,88,89,216,218,219,217,100,101,181,223,224", "startColumns": "2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2", "startOffsets": "3132,6384,10321,5161,5213,5039,5092,556,9680,9553,9613,8901,9313,9196,8960,9745,9017,9486,9073,10009,9817,9141,9427,9258,9943,9884,9370,4941,5454,8207,519,12723,12768,10567,1332,1407,1296,3809,3868,10429,10490,1369,4167,7083,8589,11753,11708,11669,8242,10265,4117,598,1745,1642,685,5849,5982,6099,6194,6289,5914,1040,1138,1209,4418,1692,13141,13085,978,4369,5621,5774,5677,632,4473,1783,13026,12965,1491,1591,1537,882,925,1252,2726,10662,10727,10836,104,161,216,340,274,445,53,5490,8167,800,743,5525,11887,2267,3567,4994,13195,8718,10076,7990,2220,2159,2099,3610,2682,2303,4297,3947,4833,3348,3467,4879,3411,2546,2486,2374,13317,13255,13383,2430,8045,3167,3290,3232,7843,7886,3702,3752,7941,3523,3651,8104,10395,5417,6441,6921,6571,6480,6878,6616,6658,6766,6820,6709,6971,7741,7792,6525,7029,7646,2763,2980,3034,3084,4774,2930,4727,4250,2883,4030,7152,8288,8526,8478,2645,4209,11847,10617,10879,10936,11797,1840,1893,1949,2006,11929,2845,8331,8388,12333,12380,10138,10191,11969,12024,12125,12218,12268,8766,8825,11308,11565,7456,7518,7273,7338,11140,11248,11461,11188,11370,4067,10980,11034,8662,12810,2058,1446,12172,4530,2606,2799,4572,4626,12447,12571,12634,12495,5279,5318,10357,12863,12915", "endColumns": "34,56,35,51,65,52,68,41,64,59,66,58,56,61,56,71,55,66,67,66,66,54,58,54,65,58,56,52,35,34,36,44,41,49,36,38,35,58,78,60,76,37,41,68,72,43,44,38,45,55,49,33,37,49,57,64,116,94,94,94,67,97,70,42,54,52,53,55,61,48,55,74,96,52,56,56,58,60,45,50,53,42,52,43,36,64,108,42,56,54,57,104,65,73,50,34,39,81,56,95,41,35,42,44,59,47,61,54,46,60,59,40,43,70,71,82,45,62,55,61,55,59,59,55,65,61,67,55,58,64,57,57,42,54,49,56,48,43,50,62,33,36,38,49,44,44,42,41,50,53,57,56,57,50,50,45,53,94,35,53,49,47,58,49,46,46,46,36,120,42,62,47,36,40,39,44,56,43,49,52,55,56,51,39,37,56,89,46,66,52,73,54,100,46,49,64,58,75,61,103,61,127,64,117,47,59,103,59,90,49,53,105,55,52,40,44,45,41,38,45,53,100,47,62,88,75,38,98,37,51,49", "endOffsets": "3164,6438,10354,5210,5276,5089,5158,595,9742,9610,9677,8957,9367,9255,9014,9814,9070,9550,9138,10073,9881,9193,9483,9310,10006,9940,9424,4991,5487,8239,553,12765,12807,10614,1366,1443,1329,3865,3944,10487,10564,1404,4206,7149,8659,11794,11750,11705,8285,10318,4164,629,1780,1689,740,5911,6096,6191,6286,6381,5979,1135,1206,1249,4470,1742,13192,13138,1037,4415,5674,5846,5771,682,4527,1837,13082,13023,1534,1639,1588,922,975,1293,2760,10724,10833,10876,158,213,271,442,337,516,101,5522,8204,879,797,5618,11926,2300,3607,5036,13252,8763,10135,8042,2264,2217,2156,3648,2723,2371,4366,4027,4876,3408,3520,4938,3464,2603,2543,2427,13380,13314,13448,2483,8101,3229,3345,3287,7883,7938,3749,3806,7987,3564,3699,8164,10426,5451,6477,6968,6613,6522,6918,6655,6706,6817,6875,6763,7026,7789,7840,6568,7080,7738,2796,3031,3081,3129,4830,2977,4771,4294,2927,4064,7270,8328,8586,8523,2679,4247,11884,10659,10933,10977,11844,1890,1946,2003,2055,11966,2880,8385,8475,12377,12444,10188,10262,12021,12122,12169,12265,12330,8822,8898,11367,11666,7515,7643,7335,7453,11185,11305,11562,11245,11458,4114,11031,11137,8715,12860,2096,1488,12215,4569,2642,2842,4623,4724,12492,12631,12720,12568,5315,5414,10392,12912,12962"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,61,74,78,81,83,86,87,88,89,91,92,93,94,95,96,99,101,102,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,148,149,150,151,152,153,154,155,156,157,158,160,161,162,163,164,165,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,321,324,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,367,368,369,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,411,412,413,414,416,421,422,423,424,425,426,427,428,429,430,431,432,433,434", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,2891,2950,2988,3042,3110,3165,3236,3280,3347,3409,3478,3539,3598,3662,3721,3795,3853,3922,3992,4061,4130,4187,4248,4305,4373,4434,4564,5626,5934,6124,6246,6469,6513,6565,6604,6722,6760,6821,6902,6965,7044,7197,7318,7389,7750,7796,7843,7884,7932,7990,8042,8078,8118,8170,8230,8297,8416,8513,8610,8707,8777,8877,8950,8995,9052,9107,9163,9221,9285,9336,9394,9471,9570,9625,9684,9743,9804,9867,9915,9968,10024,10069,10124,10380,10419,10486,10597,10642,10701,10758,10818,10925,10993,11069,11181,11218,11260,11344,11403,11501,18001,18039,18084,18131,18193,18243,18307,18364,18413,18476,18538,18581,18627,18700,18774,18859,18907,18972,19030,19094,19152,19214,19276,19334,19402,19466,19536,19594,19655,19722,19782,19842,19887,19944,19996,20055,20106,20152,23484,23659,23764,23803,23844,23896,23943,23990,24035,24079,24132,24188,24248,24307,24367,24420,24473,24521,24577,24822,24860,24916,24968,25018,25079,25131,25180,25229,25278,25317,25440,25485,25550,25600,25639,25682,26129,26176,26235,26349,26401,26456,26514,26573,26627,26669,26709,26768,26860,26909,26978,27033,27109,27166,27269,27318,27370,27437,27498,27576,27640,27746,27810,27940,28007,28127,28177,28239,28345,28407,28500,28552,28608,29184,29242,29297,29340,29460,29848,29892,29933,29981,30037,30140,30190,30255,30346,30424,30465,30566,30606,30660", "endColumns": "36,58,37,53,67,54,70,43,66,61,68,60,58,63,58,73,57,68,69,68,68,56,60,56,67,60,58,54,37,36,38,46,43,51,38,40,37,60,80,62,78,39,43,70,74,45,46,40,47,57,51,35,39,51,59,66,118,96,96,96,69,99,72,44,56,54,55,57,63,50,57,76,98,54,58,58,60,62,47,52,55,44,54,45,38,66,110,44,58,56,59,106,67,75,52,36,41,83,58,97,43,37,44,46,61,49,63,56,48,62,61,42,45,72,73,84,47,64,57,63,57,61,61,57,67,63,69,57,60,66,59,59,44,56,51,58,50,45,52,64,35,38,40,51,46,46,44,43,52,55,59,58,59,52,52,47,55,96,37,55,51,49,60,51,48,48,48,38,122,44,64,49,38,42,41,46,58,45,51,54,57,58,53,41,39,58,91,48,68,54,75,56,102,48,51,66,60,77,63,105,63,129,66,119,49,61,105,61,92,51,55,107,57,54,42,46,47,43,40,47,55,102,49,64,90,77,40,100,39,53,51", "endOffsets": "2886,2945,2983,3037,3105,3160,3231,3275,3342,3404,3473,3534,3593,3657,3716,3790,3848,3917,3987,4056,4125,4182,4243,4300,4368,4429,4488,4614,5659,5966,6158,6288,6508,6560,6599,6640,6755,6816,6897,6960,7039,7079,7236,7384,7459,7791,7838,7879,7927,7985,8037,8073,8113,8165,8225,8292,8411,8508,8605,8702,8772,8872,8945,8990,9047,9102,9158,9216,9280,9331,9389,9466,9565,9620,9679,9738,9799,9862,9910,9963,10019,10064,10119,10165,10414,10481,10592,10637,10696,10753,10813,10920,10988,11064,11117,11213,11255,11339,11398,11496,11540,18034,18079,18126,18188,18238,18302,18359,18408,18471,18533,18576,18622,18695,18769,18854,18902,18967,19025,19089,19147,19209,19271,19329,19397,19461,19531,19589,19650,19717,19777,19837,19882,19939,19991,20050,20101,20147,20200,23544,23690,23798,23839,23891,23938,23985,24030,24074,24127,24183,24243,24302,24362,24415,24468,24516,24572,24669,24855,24911,24963,25013,25074,25126,25175,25224,25273,25312,25435,25480,25545,25595,25634,25677,25719,26171,26230,26276,26396,26451,26509,26568,26622,26664,26704,26763,26855,26904,26973,27028,27104,27161,27264,27313,27365,27432,27493,27571,27635,27741,27805,27935,28002,28122,28172,28234,28340,28402,28495,28547,28603,28711,29237,29292,29335,29382,29503,29887,29928,29976,30032,30135,30185,30250,30341,30419,30460,30561,30601,30655,30707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\155e6c12dc97cf97c948c578b691358c\\transformed\\core-1.16.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "67,68,69,70,71,72,73,410", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4962,5054,5155,5249,5343,5436,5530,29083", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "5049,5150,5244,5338,5431,5525,5621,29179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9d1fcfacc0f20874a2a81b41ebfa5291\\transformed\\material3-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,860,963,1078,1160,1256,1340,1429,1535,1649,1750,1860,1968,2076,2192,2299,2400,2504,2610,2695,2790,2895,3004,3094,3192,3290,3400,3515,3615,3706,3779,3869,3958,4051,4134,4216,4308,4388,4470,4568,4662,4755,4850,4934,5030,5126,5223,5331,5411,5503", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "154,257,361,463,555,643,747,855,958,1073,1155,1251,1335,1424,1530,1644,1745,1855,1963,2071,2187,2294,2395,2499,2605,2690,2785,2890,2999,3089,3187,3285,3395,3510,3610,3701,3774,3864,3953,4046,4129,4211,4303,4383,4465,4563,4657,4750,4845,4929,5025,5121,5218,5326,5406,5498,5588"}, "to": {"startLines": "166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11545,11649,11752,11856,11958,12050,12138,12242,12350,12453,12568,12650,12746,12830,12919,13025,13139,13240,13350,13458,13566,13682,13789,13890,13994,14100,14185,14280,14385,14494,14584,14682,14780,14890,15005,15105,15196,15269,15359,15448,15541,15624,15706,15798,15878,15960,16058,16152,16245,16340,16424,16520,16616,16713,16821,16901,16993", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "11644,11747,11851,11953,12045,12133,12237,12345,12448,12563,12645,12741,12825,12914,13020,13134,13235,13345,13453,13561,13677,13784,13885,13989,14095,14180,14275,14380,14489,14579,14677,14775,14885,15000,15100,15191,15264,15354,15443,15536,15619,15701,15793,15873,15955,16053,16147,16240,16335,16419,16515,16611,16708,16816,16896,16988,17078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9e12252ef6834a61ce47908bfdf2c710\\transformed\\material-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "236", "startColumns": "4", "startOffsets": "17916", "endColumns": "84", "endOffsets": "17996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c94e4a086de3d3c71b4c9b9ecd123998\\transformed\\appcompat-1.7.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,364", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,25902", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,25976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\4a70d17a9618e65af9cd54de947c82cb\\transformed\\list-1.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "362,363", "startColumns": "4,4", "startOffsets": "25724,25814", "endColumns": "89,87", "endOffsets": "25809,25897"}}]}, {"outputFile": "com.rifsxd.ksunext.app-mergeDebugResources-77:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\baf1b711915162474bbf875d9a8baf28\\transformed\\foundation-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "60,419,420", "startColumns": "4,4,4", "startOffsets": "4493,29690,29771", "endColumns": "70,80,76", "endOffsets": "4559,29766,29843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ed59826fbda97ece0491b02ad2a5c49d\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "79,80,84,85,90,146,147,320,322,343,344,370,406,407,408,409,415,417,418", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5971,6048,6293,6378,6645,10229,10303,23406,23549,24674,24747,26281,28786,28867,28940,29012,29387,29508,29574", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "6043,6119,6373,6464,6717,10298,10375,23479,23619,24742,24817,26344,28862,28935,29007,29078,29455,29569,29685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\a64fe05c0979e98a37ab829c2d67ec24\\transformed\\core-1.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "323", "startColumns": "4", "startOffsets": "23624", "endColumns": "34", "endOffsets": "23654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ea4b31e0f2565dabf3acdd6c35348c38\\transformed\\material-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,62,63,64,65,66,75,76,77,97,98,100,145,159,223,224,225,226,227,228,229,230,231,232,233,234,235,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,325,365,366,405", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,4619,4683,4745,4815,4885,5664,5755,5861,7084,7135,7241,10170,11122,17083,17161,17222,17279,17335,17394,17452,17506,17591,17647,17705,17759,17824,20205,20279,20351,20430,20504,20580,20702,20764,20826,20925,21004,21078,21128,21179,21245,21309,21378,21449,21520,21581,21652,21719,21779,21865,21944,22011,22094,22179,22253,22318,22394,22442,22515,22579,22655,22733,22795,22859,22922,22987,23067,23143,23221,23297,23351,23695,25981,26056,28716", "endLines": "5,62,63,64,65,66,75,76,77,97,98,100,145,159,223,224,225,226,227,228,229,230,231,232,233,234,235,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,325,365,366,405", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "292,4678,4740,4810,4880,4957,5750,5856,5929,7130,7192,7313,10224,11176,17156,17217,17274,17330,17389,17447,17501,17586,17642,17700,17754,17819,17911,20274,20346,20425,20499,20575,20697,20759,20821,20920,20999,21073,21123,21174,21240,21304,21373,21444,21515,21576,21647,21714,21774,21860,21939,22006,22089,22174,22248,22313,22389,22437,22510,22574,22650,22728,22790,22854,22917,22982,23062,23138,23216,23292,23346,23401,23759,26051,26124,28781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\b0c018149e18020465f8106101906127\\transformed\\browser-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "82,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "6163,7464,7556,7657", "endColumns": "82,91,100,92", "endOffsets": "6241,7551,7652,7745"}}, {"source": "C:\\Users\\<USER>\\Desktop\\KernelSU-Next-1.0.9\\manager\\app\\src\\main\\res\\values-zh-rCN\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,685,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,740,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,61,74,78,81,83,86,87,88,89,91,92,93,94,95,96,99,101,102,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,148,149,150,151,152,153,154,155,156,157,158,160,161,162,163,164,165,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,321,324,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,367,368,369,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,411,412,413,414,416,421,422,423,424,425,426,427,428,429,430,431,432,433,434", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,2891,2950,2988,3042,3110,3165,3236,3280,3347,3409,3478,3539,3598,3662,3721,3795,3853,3922,3992,4061,4130,4187,4248,4305,4373,4434,4564,5626,5934,6124,6246,6469,6513,6565,6604,6722,6760,6821,6902,6965,7044,7197,7318,7389,7750,7796,7843,7884,7932,7990,8042,8078,8118,8170,8230,8297,8416,8513,8610,8707,8777,8877,8950,8995,9052,9107,9163,9221,9285,9336,9394,9471,9570,9625,9684,9743,9804,9867,9915,9968,10024,10069,10124,10380,10419,10486,10597,10642,10701,10758,10818,10925,10993,11069,11181,11218,11260,11344,11403,11501,18001,18039,18084,18131,18193,18243,18307,18364,18413,18476,18538,18581,18627,18700,18774,18859,18907,18972,19030,19094,19152,19214,19276,19334,19402,19466,19536,19594,19655,19722,19782,19842,19887,19944,19996,20055,20106,20152,23484,23659,23764,23803,23844,23896,23943,23990,24035,24079,24132,24188,24248,24307,24367,24420,24473,24521,24577,24822,24860,24916,24968,25018,25079,25131,25180,25229,25278,25317,25440,25485,25550,25600,25639,25682,26129,26176,26235,26349,26401,26456,26514,26573,26627,26669,26709,26768,26860,26909,26978,27033,27109,27166,27269,27318,27370,27437,27498,27576,27640,27746,27810,27940,28007,28127,28177,28239,28345,28407,28500,28552,28608,29184,29242,29297,29340,29460,29848,29892,29933,29981,30037,30140,30190,30255,30346,30424,30465,30566,30606,30660", "endColumns": "36,58,37,53,67,54,70,43,66,61,68,60,58,63,58,73,57,68,69,68,68,56,60,56,67,60,58,54,37,36,38,46,43,51,38,40,37,60,80,62,78,39,43,70,74,45,46,40,47,57,51,35,39,51,59,66,118,96,96,96,69,99,72,44,56,54,55,57,63,50,57,76,98,54,58,58,60,62,47,52,55,44,54,45,38,66,110,44,58,56,59,106,67,75,52,36,41,83,58,97,43,37,44,46,61,49,63,56,48,62,61,42,45,72,73,84,47,64,57,63,57,61,61,57,67,63,69,57,60,66,59,59,44,56,51,58,50,45,52,64,35,38,40,51,46,46,44,43,52,55,59,58,59,52,52,47,55,96,37,55,51,49,60,51,48,48,48,38,122,44,64,49,38,42,41,46,58,45,51,54,57,58,53,41,39,58,91,48,68,54,75,56,102,48,51,66,60,77,63,105,63,129,66,119,49,61,105,61,92,51,55,107,57,54,42,46,47,43,40,47,55,102,49,64,90,77,40,100,39,53,51", "endOffsets": "2886,2945,2983,3037,3105,3160,3231,3275,3342,3404,3473,3534,3593,3657,3716,3790,3848,3917,3987,4056,4125,4182,4243,4300,4368,4429,4488,4614,5659,5966,6158,6288,6508,6560,6599,6640,6755,6816,6897,6960,7039,7079,7236,7384,7459,7791,7838,7879,7927,7985,8037,8073,8113,8165,8225,8292,8411,8508,8605,8702,8772,8872,8945,8990,9047,9102,9158,9216,9280,9331,9389,9466,9565,9620,9679,9738,9799,9862,9910,9963,10019,10064,10119,10165,10414,10481,10592,10637,10696,10753,10813,10920,10988,11064,11117,11213,11255,11339,11398,11496,11540,18034,18079,18126,18188,18238,18302,18359,18408,18471,18533,18576,18622,18695,18769,18854,18902,18967,19025,19089,19147,19209,19271,19329,19397,19461,19531,19589,19650,19717,19777,19837,19882,19939,19991,20050,20101,20147,20200,23544,23690,23798,23839,23891,23938,23985,24030,24074,24127,24183,24243,24302,24362,24415,24468,24516,24572,24669,24855,24911,24963,25013,25074,25126,25175,25224,25273,25312,25435,25480,25545,25595,25634,25677,25719,26171,26230,26276,26396,26451,26509,26568,26622,26664,26704,26763,26855,26904,26973,27028,27104,27161,27264,27313,27365,27432,27493,27571,27635,27741,27805,27935,28002,28122,28172,28234,28340,28402,28495,28547,28603,28711,29237,29292,29335,29382,29503,29887,29928,29976,30032,30135,30185,30250,30341,30419,30460,30561,30601,30655,30707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\155e6c12dc97cf97c948c578b691358c\\transformed\\core-1.16.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "67,68,69,70,71,72,73,410", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4962,5054,5155,5249,5343,5436,5530,29083", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "5049,5150,5244,5338,5431,5525,5621,29179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9d1fcfacc0f20874a2a81b41ebfa5291\\transformed\\material3-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11545,11649,11752,11856,11958,12050,12138,12242,12350,12453,12568,12650,12746,12830,12919,13025,13139,13240,13350,13458,13566,13682,13789,13890,13994,14100,14185,14280,14385,14494,14584,14682,14780,14890,15005,15105,15196,15269,15359,15448,15541,15624,15706,15798,15878,15960,16058,16152,16245,16340,16424,16520,16616,16713,16821,16901,16993", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "11644,11747,11851,11953,12045,12133,12237,12345,12448,12563,12645,12741,12825,12914,13020,13134,13235,13345,13453,13561,13677,13784,13885,13989,14095,14180,14275,14380,14489,14579,14677,14775,14885,15000,15100,15191,15264,15354,15443,15536,15619,15701,15793,15873,15955,16053,16147,16240,16335,16419,16515,16611,16708,16816,16896,16988,17078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9e12252ef6834a61ce47908bfdf2c710\\transformed\\material-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "236", "startColumns": "4", "startOffsets": "17916", "endColumns": "84", "endOffsets": "17996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c94e4a086de3d3c71b4c9b9ecd123998\\transformed\\appcompat-1.7.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,364", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,25902", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,25976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\4a70d17a9618e65af9cd54de947c82cb\\transformed\\list-1.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "362,363", "startColumns": "4,4", "startOffsets": "25724,25814", "endColumns": "89,87", "endOffsets": "25809,25897"}}]}]}