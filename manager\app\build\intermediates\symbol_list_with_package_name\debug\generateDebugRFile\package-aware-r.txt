com.rifsxd.ksunext
color ic_launcher_background
drawable ic_github
drawable ic_ksu_next
drawable ic_linux
drawable ic_sus
drawable ic_telegram
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_monochrome
string about
string about_source_code
string action
string allowlist_backup
string allowlist_backup_message
string allowlist_restore
string allowlist_restore_message
string app_name
string app_profile_export_to_clipboard
string app_profile_import_export
string app_profile_import_from_clipboard
string app_profile_template_create
string app_profile_template_delete
string app_profile_template_description
string app_profile_template_edit
string app_profile_template_export_empty
string app_profile_template_id
string app_profile_template_id_exist
string app_profile_template_id_invalid
string app_profile_template_import_empty
string app_profile_template_import_success
string app_profile_template_name
string app_profile_template_readonly
string app_profile_template_save
string app_profile_template_save_failed
string app_profile_template_sync
string app_profile_template_view
string backup_restore
string cancel
string close
string confirm
string customization
string developer
string direct_install
string disable
string disabled
string enable
string enable_developer_options
string enable_developer_options_summary
string enable_web_debugging
string enable_web_debugging_summary
string enabled
string export_log
string failed_to_update_app_profile
string failed_to_update_sepolicy
string flash_failed
string flash_success
string flashing
string force_stop_app
string grant_root_failed
string hide_system_apps
string home
string home_abi
string home_android
string home_click_to_install
string home_experimental_kernelsu
string home_experimental_kernelsu_body
string home_experimental_kernelsu_body_point_1
string home_experimental_kernelsu_body_point_2
string home_experimental_kernelsu_body_point_3
string home_experimental_kernelsu_repo
string home_failure
string home_failure_tip
string home_kernel
string home_magic_mount
string home_manager_version
string home_module_count_plural
string home_module_count_singular
string home_module_update_count
string home_mount_system
string home_next_kernelsu
string home_next_kernelsu_body
string home_next_kernelsu_repo
string home_not_installed
string home_overlayfs_mount
string home_selinux_status
string home_superuser_count_plural
string home_superuser_count_singular
string home_susfs
string home_susfs_sus_su
string home_susfs_version
string home_working
string home_working_version
string hook_mode
string install
string install_inactive_slot
string install_inactive_slot_warning
string install_next
string issue_report_body
string issue_report_body_2
string issue_report_github
string issue_report_github_link
string issue_report_telegram
string issue_report_telegram_link
string issue_report_title
string later
string launch_app
string lkm_alternative_suggestion
string lkm_mode_deprecated
string lkm_warning_message
string log_saved
string module
string module_author
string module_backup
string module_backup_message
string module_changelog
string module_changelog_failed
string module_downloading
string module_empty
string module_failed_to_disable
string module_failed_to_enable
string module_id
string module_install
string module_install_prompt_with_name
string module_magisk_conflict
string module_overlay_fs_not_available
string module_restore
string module_restore_confirm
string module_restore_failed
string module_restore_message
string module_restore_success
string module_size_high_to_low
string module_size_low_to_high
string module_sort_a_to_z
string module_sort_action_first
string module_sort_enabled_first
string module_sort_webui_first
string module_sort_z_to_a
string module_start_downloading
string module_uninstall_confirm
string module_uninstall_failed
string module_uninstall_success
string module_update
string module_update_available
string module_update_json
string module_update_json_empty
string module_updated
string module_version
string module_version_code
string new_version_available
string open
string proceed
string profile
string profile_capabilities
string profile_custom
string profile_default
string profile_groups
string profile_name
string profile_namespace
string profile_namespace_global
string profile_namespace_individual
string profile_namespace_inherited
string profile_selinux_context
string profile_selinux_domain
string profile_selinux_rules
string profile_template
string profile_umount_modules
string profile_umount_modules_summary
string reboot
string reboot_bootloader
string reboot_download
string reboot_edl
string reboot_message
string reboot_recovery
string reboot_required
string reboot_to_apply
string reboot_userspace
string refresh
string require_kernel_version
string restart_app
string restart_app_message
string restart_required
string restore
string safe_mode
string save_log
string select_file
string select_file_tip
string select_kmi
string selected_lkm
string selinux_status_disabled
string selinux_status_enforcing
string selinux_status_permissive
string selinux_status_unknown
string send_log
string settings
string settings_amoled_mode
string settings_amoled_mode_summary
string settings_banner
string settings_banner_summary
string settings_check_update
string settings_check_update_summary
string settings_disable_su
string settings_disable_su_summary
string settings_language
string settings_legacyui
string settings_legacyui_summary
string settings_profile_template
string settings_profile_template_summary
string settings_restore_stock_image
string settings_restore_stock_image_message
string settings_susfs_toggle
string settings_susfs_toggle_summary
string settings_umount_modules_default
string settings_umount_modules_default_summary
string settings_uninstall
string settings_uninstall_permanent
string settings_uninstall_permanent_message
string settings_uninstall_temporary
string settings_uninstall_temporary_message
string show_system_apps
string shrink_sparse_image
string shrink_sparse_image_message
string su_not_allowed
string sucompat_disabled
string superuser
string susfs_supported
string system_default
string unavailable
string uninstall
string uninstalled
string use_overlay_fs
string use_overlay_fs_summary
string use_webuix
string use_webuix_eruda
string use_webuix_eruda_summary
string use_webuix_summary
string warning
string warning_message
string webui
string zygisk_required
string zygisk_status
style Theme_KernelSU
style Theme_KernelSU_WebUI
xml _generated_res_locale_config
xml backup_rules
xml data_extraction_rules
xml filepaths
xml network_security_config
