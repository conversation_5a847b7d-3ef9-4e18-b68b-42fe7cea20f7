{"logs": [{"outputFile": "com.rifsxd.ksunext.app-mergeDebugResources-77:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9e12252ef6834a61ce47908bfdf2c710\\transformed\\material-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "135", "startColumns": "4", "startOffsets": "13235", "endColumns": "93", "endOffsets": "13324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ed59826fbda97ece0491b02ad2a5c49d\\transformed\\ui-release\\res\\values-as\\values-as.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "206,301,386,479,577,664,761,860,949,1039,1122,1207,1286,1376,1451,1526,1600,1675,1741", "endColumns": "94,84,92,97,86,96,98,88,89,82,84,78,89,74,74,73,74,65,117", "endOffsets": "296,381,474,572,659,756,855,944,1034,1117,1202,1281,1371,1446,1521,1595,1670,1736,1854"}, "to": {"startLines": "49,50,52,53,54,62,63,181,182,184,185,189,191,192,193,194,196,197,198", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4626,4721,4914,5007,5105,5791,5888,16881,16970,17141,17224,17548,17702,17792,17867,17942,18117,18192,18258", "endColumns": "94,84,92,97,86,96,98,88,89,82,84,78,89,74,74,73,74,65,117", "endOffsets": "4716,4801,5002,5100,5187,5883,5982,16965,17055,17219,17304,17622,17787,17862,17937,18011,18187,18253,18371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\b0c018149e18020465f8106101906127\\transformed\\browser-1.8.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,105", "endOffsets": "158,264,372,478"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4806,5406,5512,5620", "endColumns": "107,105,107,105", "endOffsets": "4909,5507,5615,5721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c94e4a086de3d3c71b4c9b9ecd123998\\transformed\\appcompat-1.7.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,17309", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,17387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ea4b31e0f2565dabf3acdd6c35348c38\\transformed\\material-1.12.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,781,904,983,1043,1108,1197,1262,1321,1407,1471,1535,1598,1668,1732,1786,1891,1949,2011,2065,2137,2254,2341,2417,2509,2591,2674,2814,2891,2972,3099,3190,3267,3321,3372,3438,3508,3585,3656,3731,3802,3879,3948,4017,4124,4215,4287,4376,4465,4539,4611,4697,4747,4826,4892,4972,5056,5118,5182,5245,5314,5414,5509,5601,5693,5751,5806,5887,5968,6043", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,101,122,78,59,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,75,91,81,82,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80,80,74,74", "endOffsets": "267,349,427,504,590,674,776,899,978,1038,1103,1192,1257,1316,1402,1466,1530,1593,1663,1727,1781,1886,1944,2006,2060,2132,2249,2336,2412,2504,2586,2669,2809,2886,2967,3094,3185,3262,3316,3367,3433,3503,3580,3651,3726,3797,3874,3943,4012,4119,4210,4282,4371,4460,4534,4606,4692,4742,4821,4887,4967,5051,5113,5177,5240,5309,5409,5504,5596,5688,5746,5801,5882,5963,6038,6113"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,55,56,57,61,64,122,123,124,125,126,127,128,129,130,131,132,133,134,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3165,3247,3325,3402,3488,4322,4424,4547,5192,5252,5317,5726,5987,12302,12388,12452,12516,12579,12649,12713,12767,12872,12930,12992,13046,13118,13329,13416,13492,13584,13666,13749,13889,13966,14047,14174,14265,14342,14396,14447,14513,14583,14660,14731,14806,14877,14954,15023,15092,15199,15290,15362,15451,15540,15614,15686,15772,15822,15901,15967,16047,16131,16193,16257,16320,16389,16489,16584,16676,16768,16826,17060,17392,17473,17627", "endLines": "5,34,35,36,37,38,46,47,48,55,56,57,61,64,122,123,124,125,126,127,128,129,130,131,132,133,134,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183,187,188,190", "endColumns": "12,81,77,76,85,83,101,122,78,59,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,75,91,81,82,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80,80,74,74", "endOffsets": "317,3242,3320,3397,3483,3567,4419,4542,4621,5247,5312,5401,5786,6041,12383,12447,12511,12574,12644,12708,12762,12867,12925,12987,13041,13113,13230,13411,13487,13579,13661,13744,13884,13961,14042,14169,14260,14337,14391,14442,14508,14578,14655,14726,14801,14872,14949,15018,15087,15194,15285,15357,15446,15535,15609,15681,15767,15817,15896,15962,16042,16126,16188,16252,16315,16384,16484,16579,16671,16763,16821,16876,17136,17468,17543,17697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\155e6c12dc97cf97c948c578b691358c\\transformed\\core-1.16.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "39,40,41,42,43,44,45,195", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3572,3673,3776,3884,3989,4093,4193,18016", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3668,3771,3879,3984,4088,4188,4317,18112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\baf1b711915162474bbf875d9a8baf28\\transformed\\foundation-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,155,240", "endColumns": "99,84,87", "endOffsets": "150,235,323"}, "to": {"startLines": "33,199,200", "startColumns": "4,4,4", "startOffsets": "3065,18376,18461", "endColumns": "99,84,87", "endOffsets": "3160,18456,18544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9d1fcfacc0f20874a2a81b41ebfa5291\\transformed\\material3-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,407,525,621,715,826,970,1091,1233,1318,1416,1511,1610,1726,1854,1957,2088,2218,2347,2527,2647,2765,2889,3022,3118,3214,3335,3461,3558,3668,3776,3912,4056,4166,4268,4345,4446,4547,4653,4744,4836,4945,5025,5110,5211,5316,5414,5516,5603,5710,5809,5913,6034,6114,6217", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,105,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "169,289,402,520,616,710,821,965,1086,1228,1313,1411,1506,1605,1721,1849,1952,2083,2213,2342,2522,2642,2760,2884,3017,3113,3209,3330,3456,3553,3663,3771,3907,4051,4161,4263,4340,4441,4542,4648,4739,4831,4940,5020,5105,5206,5311,5409,5511,5598,5705,5804,5908,6029,6109,6212,6306"}, "to": {"startLines": "65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6046,6165,6285,6398,6516,6612,6706,6817,6961,7082,7224,7309,7407,7502,7601,7717,7845,7948,8079,8209,8338,8518,8638,8756,8880,9013,9109,9205,9326,9452,9549,9659,9767,9903,10047,10157,10259,10336,10437,10538,10644,10735,10827,10936,11016,11101,11202,11307,11405,11507,11594,11701,11800,11904,12025,12105,12208", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,105,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "6160,6280,6393,6511,6607,6701,6812,6956,7077,7219,7304,7402,7497,7596,7712,7840,7943,8074,8204,8333,8513,8633,8751,8875,9008,9104,9200,9321,9447,9544,9654,9762,9898,10042,10152,10254,10331,10432,10533,10639,10730,10822,10931,11011,11096,11197,11302,11400,11502,11589,11696,11795,11899,12020,12100,12203,12297"}}]}]}