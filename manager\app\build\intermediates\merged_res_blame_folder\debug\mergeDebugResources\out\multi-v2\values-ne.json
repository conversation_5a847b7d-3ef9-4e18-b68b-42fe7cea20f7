{"logs": [{"outputFile": "com.rifsxd.ksunext.app-mergeDebugResources-77:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\a64fe05c0979e98a37ab829c2d67ec24\\transformed\\core-1.3.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,105", "endColumns": "49,36", "endOffsets": "100,137"}, "to": {"startLines": "46,184", "startColumns": "4,4", "startOffsets": "4315,17518", "endColumns": "49,36", "endOffsets": "4360,17550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9e12252ef6834a61ce47908bfdf2c710\\transformed\\material-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "136", "startColumns": "4", "startOffsets": "13540", "endColumns": "87", "endOffsets": "13623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9d1fcfacc0f20874a2a81b41ebfa5291\\transformed\\material3-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,303,419,547,646,741,853,1005,1126,1279,1363,1471,1569,1668,1780,1904,2017,2163,2306,2440,2605,2735,2887,3044,3173,3272,3367,3483,3607,3711,3830,3940,4086,4234,4344,4452,4527,4632,4737,4848,4939,5034,5141,5221,5306,5407,5516,5611,5714,5801,5912,6011,6116,6239,6319,6425", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "179,298,414,542,641,736,848,1000,1121,1274,1358,1466,1564,1663,1775,1899,2012,2158,2301,2435,2600,2730,2882,3039,3168,3267,3362,3478,3602,3706,3825,3935,4081,4229,4339,4447,4522,4627,4732,4843,4934,5029,5136,5216,5301,5402,5511,5606,5709,5796,5907,6006,6111,6234,6314,6420,6514"}, "to": {"startLines": "66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6092,6221,6340,6456,6584,6683,6778,6890,7042,7163,7316,7400,7508,7606,7705,7817,7941,8054,8200,8343,8477,8642,8772,8924,9081,9210,9309,9404,9520,9644,9748,9867,9977,10123,10271,10381,10489,10564,10669,10774,10885,10976,11071,11178,11258,11343,11444,11553,11648,11751,11838,11949,12048,12153,12276,12356,12462", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "6216,6335,6451,6579,6678,6773,6885,7037,7158,7311,7395,7503,7601,7700,7812,7936,8049,8195,8338,8472,8637,8767,8919,9076,9205,9304,9399,9515,9639,9743,9862,9972,10118,10266,10376,10484,10559,10664,10769,10880,10971,11066,11173,11253,11338,11439,11548,11643,11746,11833,11944,12043,12148,12271,12351,12457,12551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\baf1b711915162474bbf875d9a8baf28\\transformed\\foundation-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,90", "endOffsets": "123,208,299"}, "to": {"startLines": "33,203,204", "startColumns": "4,4,4", "startOffsets": "3079,19122,19207", "endColumns": "72,84,90", "endOffsets": "3147,19202,19293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ed59826fbda97ece0491b02ad2a5c49d\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,278,368,462,559,645,727,823,910,996,1086,1179,1256,1340,1415,1488,1560,1641,1709", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,83,74,72,71,80,67,119", "endOffsets": "273,363,457,554,640,722,818,905,991,1081,1174,1251,1335,1410,1483,1555,1636,1704,1824"}, "to": {"startLines": "50,51,53,54,55,63,64,182,183,186,187,193,195,196,197,198,200,201,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4664,4763,4954,5048,5145,5853,5935,17345,17432,17639,17729,18298,18448,18532,18607,18680,18853,18934,19002", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,83,74,72,71,80,67,119", "endOffsets": "4758,4848,5043,5140,5226,5930,6026,17427,17513,17724,17817,18370,18527,18602,18675,18747,18929,18997,19117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\155e6c12dc97cf97c948c578b691358c\\transformed\\core-1.16.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "39,40,41,42,43,44,45,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3595,3698,3801,3903,4009,4107,4207,18752", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3693,3796,3898,4004,4102,4202,4310,18848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\4a70d17a9618e65af9cd54de947c82cb\\transformed\\list-1.3.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "17822,17931", "endColumns": "108,121", "endOffsets": "17926,18048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ea4b31e0f2565dabf3acdd6c35348c38\\transformed\\material-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1064,1130,1226,1292,1353,1458,1522,1594,1652,1726,1788,1842,1955,2015,2076,2135,2213,2337,2418,2500,2600,2685,2770,2906,2987,3070,3201,3284,3370,3432,3486,3552,3629,3708,3779,3862,3931,4007,4088,4156,4260,4351,4429,4522,4619,4693,4772,4870,4930,5018,5084,5172,5260,5322,5390,5453,5519,5624,5730,5825,5930,5996,6054,6138,6227,6303", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "257,346,434,516,611,700,802,912,999,1059,1125,1221,1287,1348,1453,1517,1589,1647,1721,1783,1837,1950,2010,2071,2130,2208,2332,2413,2495,2595,2680,2765,2901,2982,3065,3196,3279,3365,3427,3481,3547,3624,3703,3774,3857,3926,4002,4083,4151,4255,4346,4424,4517,4614,4688,4767,4865,4925,5013,5079,5167,5255,5317,5385,5448,5514,5619,5725,5820,5925,5991,6049,6133,6222,6298,6371"}, "to": {"startLines": "2,34,35,36,37,38,47,48,49,56,57,58,62,65,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,185,191,192,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3152,3241,3329,3411,3506,4365,4467,4577,5231,5291,5357,5787,6031,12556,12661,12725,12797,12855,12929,12991,13045,13158,13218,13279,13338,13416,13628,13709,13791,13891,13976,14061,14197,14278,14361,14492,14575,14661,14723,14777,14843,14920,14999,15070,15153,15222,15298,15379,15447,15551,15642,15720,15813,15910,15984,16063,16161,16221,16309,16375,16463,16551,16613,16681,16744,16810,16915,17021,17116,17221,17287,17555,18133,18222,18375", "endLines": "5,34,35,36,37,38,47,48,49,56,57,58,62,65,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,185,191,192,194", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "307,3236,3324,3406,3501,3590,4462,4572,4659,5286,5352,5448,5848,6087,12656,12720,12792,12850,12924,12986,13040,13153,13213,13274,13333,13411,13535,13704,13786,13886,13971,14056,14192,14273,14356,14487,14570,14656,14718,14772,14838,14915,14994,15065,15148,15217,15293,15374,15442,15546,15637,15715,15808,15905,15979,16058,16156,16216,16304,16370,16458,16546,16608,16676,16739,16805,16910,17016,17111,17216,17282,17340,17634,18217,18293,18443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c94e4a086de3d3c71b4c9b9ecd123998\\transformed\\appcompat-1.7.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2461,2574,2684,2801,2968,18053", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2456,2569,2679,2796,2963,3074,18128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\b0c018149e18020465f8106101906127\\transformed\\browser-1.8.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,268,382", "endColumns": "100,111,113,107", "endOffsets": "151,263,377,485"}, "to": {"startLines": "52,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "4853,5453,5565,5679", "endColumns": "100,111,113,107", "endOffsets": "4949,5560,5674,5782"}}]}]}