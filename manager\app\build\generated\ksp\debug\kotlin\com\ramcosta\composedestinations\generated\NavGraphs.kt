package com.ramcosta.composedestinations.generated

import com.ramcosta.composedestinations.annotation.RootGraph
import com.ramcosta.composedestinations.generated.destinations.*
import com.ramcosta.composedestinations.generated.navgraphs.*
import com.ramcosta.composedestinations.spec.*
import com.rifsxd.ksunext.ui.screen.HomeScreen

/**
 * Class generated if any Composable is annotated with `@Destination`.
 * It aggregates all [DestinationSpec]s in their [NavGraphSpec]s.
 *
 * -------------------------------------------------------
 * **Legend:**                                           
 * * 🗺️: Navigation graph                              
 * * 📍: Destination                                   
 * * 🏁: Marks 🗺️/📍as the start of the parent graph   
 * * 🧩: Means 🗺️/📍is generated on external module          
 * -------------------------------------------------------
 *
 * * 🗺️[RootGraph]
 * * ∙∙∙∙∙∙∙∙↳📍🏁[HomeScreen]
 */
internal object NavGraphs {

    val root = RootNavGraph
}