int color ic_launcher_background 0x0
int drawable ic_github 0x0
int drawable ic_ksu_next 0x0
int drawable ic_linux 0x0
int drawable ic_sus 0x0
int drawable ic_telegram 0x0
int mipmap ic_launcher 0x0
int mipmap ic_launcher_foreground 0x0
int mipmap ic_launcher_monochrome 0x0
int string about 0x0
int string about_source_code 0x0
int string action 0x0
int string allowlist_backup 0x0
int string allowlist_backup_message 0x0
int string allowlist_restore 0x0
int string allowlist_restore_message 0x0
int string app_name 0x0
int string app_profile_export_to_clipboard 0x0
int string app_profile_import_export 0x0
int string app_profile_import_from_clipboard 0x0
int string app_profile_template_create 0x0
int string app_profile_template_delete 0x0
int string app_profile_template_description 0x0
int string app_profile_template_edit 0x0
int string app_profile_template_export_empty 0x0
int string app_profile_template_id 0x0
int string app_profile_template_id_exist 0x0
int string app_profile_template_id_invalid 0x0
int string app_profile_template_import_empty 0x0
int string app_profile_template_import_success 0x0
int string app_profile_template_name 0x0
int string app_profile_template_readonly 0x0
int string app_profile_template_save 0x0
int string app_profile_template_save_failed 0x0
int string app_profile_template_sync 0x0
int string app_profile_template_view 0x0
int string backup_restore 0x0
int string cancel 0x0
int string close 0x0
int string confirm 0x0
int string customization 0x0
int string developer 0x0
int string direct_install 0x0
int string disable 0x0
int string disabled 0x0
int string enable 0x0
int string enable_developer_options 0x0
int string enable_developer_options_summary 0x0
int string enable_web_debugging 0x0
int string enable_web_debugging_summary 0x0
int string enabled 0x0
int string export_log 0x0
int string failed_to_update_app_profile 0x0
int string failed_to_update_sepolicy 0x0
int string flash_failed 0x0
int string flash_success 0x0
int string flashing 0x0
int string force_stop_app 0x0
int string grant_root_failed 0x0
int string hide_system_apps 0x0
int string home 0x0
int string home_abi 0x0
int string home_android 0x0
int string home_click_to_install 0x0
int string home_experimental_kernelsu 0x0
int string home_experimental_kernelsu_body 0x0
int string home_experimental_kernelsu_body_point_1 0x0
int string home_experimental_kernelsu_body_point_2 0x0
int string home_experimental_kernelsu_body_point_3 0x0
int string home_experimental_kernelsu_repo 0x0
int string home_failure 0x0
int string home_failure_tip 0x0
int string home_kernel 0x0
int string home_magic_mount 0x0
int string home_manager_version 0x0
int string home_module_count_plural 0x0
int string home_module_count_singular 0x0
int string home_module_update_count 0x0
int string home_mount_system 0x0
int string home_next_kernelsu 0x0
int string home_next_kernelsu_body 0x0
int string home_next_kernelsu_repo 0x0
int string home_not_installed 0x0
int string home_overlayfs_mount 0x0
int string home_selinux_status 0x0
int string home_superuser_count_plural 0x0
int string home_superuser_count_singular 0x0
int string home_susfs 0x0
int string home_susfs_sus_su 0x0
int string home_susfs_version 0x0
int string home_working 0x0
int string home_working_version 0x0
int string hook_mode 0x0
int string install 0x0
int string install_inactive_slot 0x0
int string install_inactive_slot_warning 0x0
int string install_next 0x0
int string issue_report_body 0x0
int string issue_report_body_2 0x0
int string issue_report_github 0x0
int string issue_report_github_link 0x0
int string issue_report_telegram 0x0
int string issue_report_telegram_link 0x0
int string issue_report_title 0x0
int string later 0x0
int string launch_app 0x0
int string lkm_alternative_suggestion 0x0
int string lkm_mode_deprecated 0x0
int string lkm_warning_message 0x0
int string log_saved 0x0
int string module 0x0
int string module_author 0x0
int string module_backup 0x0
int string module_backup_message 0x0
int string module_changelog 0x0
int string module_changelog_failed 0x0
int string module_downloading 0x0
int string module_empty 0x0
int string module_failed_to_disable 0x0
int string module_failed_to_enable 0x0
int string module_id 0x0
int string module_install 0x0
int string module_install_prompt_with_name 0x0
int string module_magisk_conflict 0x0
int string module_overlay_fs_not_available 0x0
int string module_restore 0x0
int string module_restore_confirm 0x0
int string module_restore_failed 0x0
int string module_restore_message 0x0
int string module_restore_success 0x0
int string module_size_high_to_low 0x0
int string module_size_low_to_high 0x0
int string module_sort_a_to_z 0x0
int string module_sort_action_first 0x0
int string module_sort_enabled_first 0x0
int string module_sort_webui_first 0x0
int string module_sort_z_to_a 0x0
int string module_start_downloading 0x0
int string module_uninstall_confirm 0x0
int string module_uninstall_failed 0x0
int string module_uninstall_success 0x0
int string module_update 0x0
int string module_update_available 0x0
int string module_update_json 0x0
int string module_update_json_empty 0x0
int string module_updated 0x0
int string module_version 0x0
int string module_version_code 0x0
int string new_version_available 0x0
int string open 0x0
int string proceed 0x0
int string profile 0x0
int string profile_capabilities 0x0
int string profile_custom 0x0
int string profile_default 0x0
int string profile_groups 0x0
int string profile_name 0x0
int string profile_namespace 0x0
int string profile_namespace_global 0x0
int string profile_namespace_individual 0x0
int string profile_namespace_inherited 0x0
int string profile_selinux_context 0x0
int string profile_selinux_domain 0x0
int string profile_selinux_rules 0x0
int string profile_template 0x0
int string profile_umount_modules 0x0
int string profile_umount_modules_summary 0x0
int string reboot 0x0
int string reboot_bootloader 0x0
int string reboot_download 0x0
int string reboot_edl 0x0
int string reboot_message 0x0
int string reboot_recovery 0x0
int string reboot_required 0x0
int string reboot_to_apply 0x0
int string reboot_userspace 0x0
int string refresh 0x0
int string require_kernel_version 0x0
int string restart_app 0x0
int string restart_app_message 0x0
int string restart_required 0x0
int string restore 0x0
int string safe_mode 0x0
int string save_log 0x0
int string select_file 0x0
int string select_file_tip 0x0
int string select_kmi 0x0
int string selected_lkm 0x0
int string selinux_status_disabled 0x0
int string selinux_status_enforcing 0x0
int string selinux_status_permissive 0x0
int string selinux_status_unknown 0x0
int string send_log 0x0
int string settings 0x0
int string settings_amoled_mode 0x0
int string settings_amoled_mode_summary 0x0
int string settings_banner 0x0
int string settings_banner_summary 0x0
int string settings_check_update 0x0
int string settings_check_update_summary 0x0
int string settings_disable_su 0x0
int string settings_disable_su_summary 0x0
int string settings_language 0x0
int string settings_legacyui 0x0
int string settings_legacyui_summary 0x0
int string settings_profile_template 0x0
int string settings_profile_template_summary 0x0
int string settings_restore_stock_image 0x0
int string settings_restore_stock_image_message 0x0
int string settings_susfs_toggle 0x0
int string settings_susfs_toggle_summary 0x0
int string settings_umount_modules_default 0x0
int string settings_umount_modules_default_summary 0x0
int string settings_uninstall 0x0
int string settings_uninstall_permanent 0x0
int string settings_uninstall_permanent_message 0x0
int string settings_uninstall_temporary 0x0
int string settings_uninstall_temporary_message 0x0
int string show_system_apps 0x0
int string shrink_sparse_image 0x0
int string shrink_sparse_image_message 0x0
int string su_not_allowed 0x0
int string sucompat_disabled 0x0
int string superuser 0x0
int string susfs_supported 0x0
int string system_default 0x0
int string unavailable 0x0
int string uninstall 0x0
int string uninstalled 0x0
int string use_overlay_fs 0x0
int string use_overlay_fs_summary 0x0
int string use_webuix 0x0
int string use_webuix_eruda 0x0
int string use_webuix_eruda_summary 0x0
int string use_webuix_summary 0x0
int string warning 0x0
int string warning_message 0x0
int string webui 0x0
int string zygisk_required 0x0
int string zygisk_status 0x0
int style Theme_KernelSU 0x0
int style Theme_KernelSU_WebUI 0x0
int xml _generated_res_locale_config 0x0
int xml backup_rules 0x0
int xml data_extraction_rules 0x0
int xml filepaths 0x0
int xml network_security_config 0x0
