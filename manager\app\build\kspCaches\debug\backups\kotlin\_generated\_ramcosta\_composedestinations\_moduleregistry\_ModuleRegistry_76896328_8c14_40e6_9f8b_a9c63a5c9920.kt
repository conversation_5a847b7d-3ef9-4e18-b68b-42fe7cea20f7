package _generated._ramcosta._composedestinations._moduleregistry

import com.ramcosta.composedestinations.spec.DestinationSpec

public annotation class _Info_76896328_8c14_40e6_9f8b_a9c63a5c9920(
    val moduleName: String,
    val packageName: String,
    val hasNavArgsPackage: Boolean,
    val typeResults: Array<_Destination_Result_Info_76896328_8c14_40e6_9f8b_a9c63a5c9920> = emptyArray(),
    val topLevelGraphs: Array<String> = emptyArray()
)

public annotation class _Destination_Result_Info_76896328_8c14_40e6_9f8b_a9c63a5c9920(
    val destination: String,
    val resultType: String,
    val resultNavType: String,
    val isResultNullable: Boolean
)

@_Info_76896328_8c14_40e6_9f8b_a9c63a5c9920(
    moduleName = "",
    packageName = "com.ramcosta.composedestinations.generated",
    hasNavArgsPackage = false,
    typeResults = [

    ],
    topLevelGraphs = [
		"RootNavGraph"
    ]
)
public object _ModuleRegistry_76896328_8c14_40e6_9f8b_a9c63a5c9920