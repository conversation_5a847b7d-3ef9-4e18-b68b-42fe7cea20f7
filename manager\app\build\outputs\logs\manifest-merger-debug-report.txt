-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:53:9-61:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:57:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:55:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:56:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:54:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:2:1-64:12
INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:2:1-64:12
INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:2:1-64:12
INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:2:1-64:12
MERGED from [com.github.MMRLApp.MMRL:ui:v33890] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ca18dd294b0fb99d8d87544a773ff539\transformed\ui-v33890\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.MMRLApp.MMRL:ext:v33890] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b0cc445ea1bb5644cd527ee56fb91051\transformed\ext-v33890\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.raamcosta.compose-destinations:core:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5a57f0620b48825258c8115b9c6e3d6e\transformed\core-2.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1dd9fa2792b5bbbd75a3ad331542cc8f\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4d5f605650626d63a91fcf3061c03528\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fed4731206726febed04708df4dd4dba\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ea4b31e0f2565dabf3acdd6c35348c38\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [me.zhanghai.android.appiconloader:appiconloader-coil:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\04bf4942c13c0057c4ee7c88bda3f3a8\transformed\appiconloader-coil-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3a3de0ddb97247e079ce81ea4e2056d6\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ffe62cedf756a4d78fc6126ffa61cb2c\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e5052e4e27b7bdc225aa68f66832364e\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b446106d629f4c2c77cb0dd8c58c017\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\217490c617f2083ec9455feb8d093884\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\10d65ddce01e4932cc4c37b13a054d85\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c94e4a086de3d3c71b4c9b9ecd123998\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c694124458e99da89e104d2a7d200886\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\238ced3a9b3f51952b3bffbdcea8a7ef\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [com.maxkeppeler.sheets-compose-dialogs:list:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4a70d17a9618e65af9cd54de947c82cb\transformed\list-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.maxkeppeler.sheets-compose-dialogs:input:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2ab727fea2fb5fe53af44a9d8406f10\transformed\input-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.maxkeppeler.sheets-compose-dialogs:core:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a64fe05c0979e98a37ab829c2d67ec24\transformed\core-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9d1fcfacc0f20874a2a81b41ebfa5291\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d01e627e9577684ffa77e3ca1fb9fee6\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\db1d2f4e9ffffbb7b55526cfbc7ef958\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\acd5d6b719ee9b3535670341970689a7\transformed\animation-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b82047ccfef97a2327cebcfaa434a439\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\baf1b711915162474bbf875d9a8baf28\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0809adf6a6c0c310fd79a1cfe7adab8e\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6e77e4e27f3374566fcee63448d1abe1\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4e67c9b5abc1a4fa818c153188619267\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\673471cc018557b1266ca22ed041c697\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\28d6e8770a38434951b3e40de0f05f6f\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6fe435a64d8c3b746352a11947bc205f\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5725761e66954bb7ffc1570db0fda4c1\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a86bf1601fe4bdcdddebaac71e753aae\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9e12252ef6834a61ce47908bfdf2c710\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\957097c905b3a03058a22c42f83f4a1e\transformed\webkit-1.14.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0f2edea19ed7c52126ddbbf7c2ec3591\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d3f21169ea63851d3f6fb8571de53a6\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57560b3ae61ae14795106238695488a1\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b792f167289eb31b2ac98c49f48d177\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\375c102fcbc5234bf509d3fb84297456\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5b9a7439854894a020c102002a788bd4\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d903b4195a79342c0f2ebffc4ba2dde4\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d0567795528d391f03de34ec3121bc1f\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\44a88091afbd6487b85a0aaa7afc21e6\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f58017cf0025d1dd0a433548614d1ac8\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\54ccacfe69105653e8bb6849816caf48\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9feb22e08b25699a42a895c217fa5426\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6839b516fcaddd1d65095bf201d04422\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\87f28987d8b7555203b1cf446f29a975\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b0c018149e18020465f8106101906127\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c0097e53cf984a51221009be9186bbc4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cc4b93bdc34efc4bdd33e07042571af7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ae41cc1eaebec50c82ec2efb31ac70d6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3eb1a99bc08732c380cbbb5c9bbbf5ae\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\64f540343626c73edc6ad22e7912e41a\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\666af40445825b60b96d02b304bb7677\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\17388649ae338a23501118f801f51a80\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\be49cfe65380b018d85519986ffa8500\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\09de227f7928c32c57a964dd7c9104b4\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\80a8ab1f9784ee28660c0b3e9538b6b6\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\534cac120d403368e589e5d446a1df03\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4fde6db967e9edf68f940b6c7d49ecc1\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\77782dfd18d860eaba833f2491071ee1\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b1ecffd73d5b4fe67a3b24cdd12d0789\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b5ee2568eeb9cb53cd2b1f7913a14d20\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\803cc0ed5a73bb3543061cde73284b06\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52c791ee2ea6e83732c62768087b85ca\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\602265030a5d2ede7b3df78d87cc45e6\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\91548cbd40b0dfa48c8f344b9b5c4376\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b0c87a9ac610e46df845b09d0617695\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7aabc0c58e181a57fddf1fefee3f041e\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed59826fbda97ece0491b02ad2a5c49d\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\177e444593e4b7a8044efce760dac824\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3e96119c15d18892a7b6c93ef99f8ffa\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cd89981e8e257c118ffb880ca7cb4ae3\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\520ae8b17e80c30458dbdc30b8b89325\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6c992f49cd5dd6e821cb79fb6aed0059\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d770120abe1e97d6594c17a45ee9bc5\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.noties.markwon:core:4.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\25c68a4d718f989d520ebcdf5aba741a\transformed\core-4.6.2\AndroidManifest.xml:2:1-11:12
MERGED from [me.zhanghai.android.appiconloader:appiconloader:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ab0066980fb3a4a2a408f5415fba5d1e\transformed\appiconloader-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [me.zhanghai.android.appiconloader:appiconloader-iconloaderlib:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b24f98db99e9c5a3a4eb126660e4798c\transformed\appiconloader-iconloaderlib-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fda6ee821203d0ff67c9bee29fde0c8d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\56282bb99fd1c1c3e4b8a4f68b8b43dd\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ea06cca3af6b852689a5c0ce647845a8\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\de2e1a3f998b6450e20c48e5db4243cd\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e66c1b829a791e58da9f30a58f9fd2c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\14db14e8f77974cddd9a2c171ab5fc15\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\48c9d3c93c0ff39da09afcf242af84ce\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4bab32fe4f70edfeb1c454bf36b092fa\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dac76183d0c97b1e012334ab33da5939\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\06e6932f21585abcbc3515ea64448862\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1d0e67c8877d8075be21723e49843c4e\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d1f7850da3e3bf76a65862abef43f684\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.topjohnwu.libsu:service:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0aa38518544eca4718361df7fd0b0571\transformed\service-6.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.topjohnwu.libsu:io:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5ebf347b78b43198723a272c7654512d\transformed\io-6.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.topjohnwu.libsu:core:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\12384b829e3c3ee115d21e992b3f035a\transformed\core-6.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [dev.rikka.rikkax.parcelablelist:parcelablelist:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\538d41cd23daafbb8879ca4f42f2f664\transformed\parcelablelist-2.0.1-debug\AndroidManifest.xml:2:1-9:12
MERGED from [org.lsposed.libcxx:libcxx:28.1.13356709] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\290d87afc4f3d4d5661561da4a4f63ee\transformed\libcxx-28.1.13356709\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.topjohnwu.libsu:nio:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fee123e0e31195b9dd319e38b0414583\transformed\nio-6.0.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:6:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:6:22-74
application
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:8:5-62:19
INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:8:5-62:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ea4b31e0f2565dabf3acdd6c35348c38\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ea4b31e0f2565dabf3acdd6c35348c38\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\10d65ddce01e4932cc4c37b13a054d85\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\10d65ddce01e4932cc4c37b13a054d85\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e66c1b829a791e58da9f30a58f9fd2c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e66c1b829a791e58da9f30a58f9fd2c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\48c9d3c93c0ff39da09afcf242af84ce\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\48c9d3c93c0ff39da09afcf242af84ce\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:13:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:10:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:18:9-46
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:16:9-69
	android:enableOnBackInvokedCallback
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:12:9-51
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:11:9-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:9:9-44
activity#com.rifsxd.ksunext.ui.MainActivity
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:20:9-37:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:23:13-50
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:21:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:24:13-27:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:25:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:25:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:26:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:26:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:mimeType:application/zip+data:pathPattern:.*\\.zip+data:scheme:content+data:scheme:file
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:28:13-36:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:30:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:30:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:31:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:31:27-75
data
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:32:17-60
	android:scheme
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:33:23-44
	android:pathPattern
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:35:23-53
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:32:23-57
activity#com.rifsxd.ksunext.ui.webui.WebUIActivity
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:39:9-44:59
	android:autoRemoveFromRecents
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:41:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:43:13-37
	android:documentLaunchMode
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:42:13-54
	android:theme
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:44:13-56
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:40:13-51
activity#com.rifsxd.ksunext.ui.webui.WebUIXActivity
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:46:9-51:59
	android:autoRemoveFromRecents
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:48:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:50:13-37
	android:documentLaunchMode
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:49:13-54
	android:theme
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:51:13-56
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:47:13-52
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:58:13-60:53
	android:resource
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:60:17-50
	android:name
		ADDED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml:59:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml
MERGED from [com.github.MMRLApp.MMRL:ui:v33890] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ca18dd294b0fb99d8d87544a773ff539\transformed\ui-v33890\AndroidManifest.xml:5:5-44
MERGED from [com.github.MMRLApp.MMRL:ui:v33890] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ca18dd294b0fb99d8d87544a773ff539\transformed\ui-v33890\AndroidManifest.xml:5:5-44
MERGED from [com.github.MMRLApp.MMRL:ext:v33890] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b0cc445ea1bb5644cd527ee56fb91051\transformed\ext-v33890\AndroidManifest.xml:5:5-44
MERGED from [com.github.MMRLApp.MMRL:ext:v33890] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b0cc445ea1bb5644cd527ee56fb91051\transformed\ext-v33890\AndroidManifest.xml:5:5-44
MERGED from [io.github.raamcosta.compose-destinations:core:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5a57f0620b48825258c8115b9c6e3d6e\transformed\core-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.raamcosta.compose-destinations:core:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5a57f0620b48825258c8115b9c6e3d6e\transformed\core-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1dd9fa2792b5bbbd75a3ad331542cc8f\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1dd9fa2792b5bbbd75a3ad331542cc8f\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4d5f605650626d63a91fcf3061c03528\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4d5f605650626d63a91fcf3061c03528\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fed4731206726febed04708df4dd4dba\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fed4731206726febed04708df4dd4dba\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ea4b31e0f2565dabf3acdd6c35348c38\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ea4b31e0f2565dabf3acdd6c35348c38\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [me.zhanghai.android.appiconloader:appiconloader-coil:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\04bf4942c13c0057c4ee7c88bda3f3a8\transformed\appiconloader-coil-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [me.zhanghai.android.appiconloader:appiconloader-coil:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\04bf4942c13c0057c4ee7c88bda3f3a8\transformed\appiconloader-coil-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3a3de0ddb97247e079ce81ea4e2056d6\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3a3de0ddb97247e079ce81ea4e2056d6\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ffe62cedf756a4d78fc6126ffa61cb2c\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ffe62cedf756a4d78fc6126ffa61cb2c\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e5052e4e27b7bdc225aa68f66832364e\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e5052e4e27b7bdc225aa68f66832364e\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b446106d629f4c2c77cb0dd8c58c017\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b446106d629f4c2c77cb0dd8c58c017\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\217490c617f2083ec9455feb8d093884\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\217490c617f2083ec9455feb8d093884\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\10d65ddce01e4932cc4c37b13a054d85\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\10d65ddce01e4932cc4c37b13a054d85\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c94e4a086de3d3c71b4c9b9ecd123998\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c94e4a086de3d3c71b4c9b9ecd123998\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c694124458e99da89e104d2a7d200886\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c694124458e99da89e104d2a7d200886\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\238ced3a9b3f51952b3bffbdcea8a7ef\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\238ced3a9b3f51952b3bffbdcea8a7ef\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [com.maxkeppeler.sheets-compose-dialogs:list:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4a70d17a9618e65af9cd54de947c82cb\transformed\list-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.maxkeppeler.sheets-compose-dialogs:list:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4a70d17a9618e65af9cd54de947c82cb\transformed\list-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.maxkeppeler.sheets-compose-dialogs:input:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2ab727fea2fb5fe53af44a9d8406f10\transformed\input-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.maxkeppeler.sheets-compose-dialogs:input:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2ab727fea2fb5fe53af44a9d8406f10\transformed\input-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.maxkeppeler.sheets-compose-dialogs:core:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a64fe05c0979e98a37ab829c2d67ec24\transformed\core-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.maxkeppeler.sheets-compose-dialogs:core:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a64fe05c0979e98a37ab829c2d67ec24\transformed\core-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9d1fcfacc0f20874a2a81b41ebfa5291\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9d1fcfacc0f20874a2a81b41ebfa5291\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d01e627e9577684ffa77e3ca1fb9fee6\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d01e627e9577684ffa77e3ca1fb9fee6\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\db1d2f4e9ffffbb7b55526cfbc7ef958\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\db1d2f4e9ffffbb7b55526cfbc7ef958\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\acd5d6b719ee9b3535670341970689a7\transformed\animation-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\acd5d6b719ee9b3535670341970689a7\transformed\animation-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b82047ccfef97a2327cebcfaa434a439\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b82047ccfef97a2327cebcfaa434a439\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\baf1b711915162474bbf875d9a8baf28\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\baf1b711915162474bbf875d9a8baf28\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0809adf6a6c0c310fd79a1cfe7adab8e\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0809adf6a6c0c310fd79a1cfe7adab8e\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6e77e4e27f3374566fcee63448d1abe1\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6e77e4e27f3374566fcee63448d1abe1\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4e67c9b5abc1a4fa818c153188619267\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4e67c9b5abc1a4fa818c153188619267\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\673471cc018557b1266ca22ed041c697\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\673471cc018557b1266ca22ed041c697\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\28d6e8770a38434951b3e40de0f05f6f\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\28d6e8770a38434951b3e40de0f05f6f\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6fe435a64d8c3b746352a11947bc205f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6fe435a64d8c3b746352a11947bc205f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5725761e66954bb7ffc1570db0fda4c1\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5725761e66954bb7ffc1570db0fda4c1\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a86bf1601fe4bdcdddebaac71e753aae\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a86bf1601fe4bdcdddebaac71e753aae\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9e12252ef6834a61ce47908bfdf2c710\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9e12252ef6834a61ce47908bfdf2c710\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\957097c905b3a03058a22c42f83f4a1e\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\957097c905b3a03058a22c42f83f4a1e\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0f2edea19ed7c52126ddbbf7c2ec3591\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0f2edea19ed7c52126ddbbf7c2ec3591\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d3f21169ea63851d3f6fb8571de53a6\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d3f21169ea63851d3f6fb8571de53a6\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57560b3ae61ae14795106238695488a1\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\57560b3ae61ae14795106238695488a1\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b792f167289eb31b2ac98c49f48d177\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b792f167289eb31b2ac98c49f48d177\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\375c102fcbc5234bf509d3fb84297456\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\375c102fcbc5234bf509d3fb84297456\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5b9a7439854894a020c102002a788bd4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5b9a7439854894a020c102002a788bd4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d903b4195a79342c0f2ebffc4ba2dde4\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d903b4195a79342c0f2ebffc4ba2dde4\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d0567795528d391f03de34ec3121bc1f\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d0567795528d391f03de34ec3121bc1f\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\44a88091afbd6487b85a0aaa7afc21e6\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\44a88091afbd6487b85a0aaa7afc21e6\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f58017cf0025d1dd0a433548614d1ac8\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f58017cf0025d1dd0a433548614d1ac8\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\54ccacfe69105653e8bb6849816caf48\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\54ccacfe69105653e8bb6849816caf48\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9feb22e08b25699a42a895c217fa5426\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9feb22e08b25699a42a895c217fa5426\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6839b516fcaddd1d65095bf201d04422\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6839b516fcaddd1d65095bf201d04422\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\87f28987d8b7555203b1cf446f29a975\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\87f28987d8b7555203b1cf446f29a975\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b0c018149e18020465f8106101906127\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b0c018149e18020465f8106101906127\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c0097e53cf984a51221009be9186bbc4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c0097e53cf984a51221009be9186bbc4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cc4b93bdc34efc4bdd33e07042571af7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cc4b93bdc34efc4bdd33e07042571af7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ae41cc1eaebec50c82ec2efb31ac70d6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ae41cc1eaebec50c82ec2efb31ac70d6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3eb1a99bc08732c380cbbb5c9bbbf5ae\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3eb1a99bc08732c380cbbb5c9bbbf5ae\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\64f540343626c73edc6ad22e7912e41a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\64f540343626c73edc6ad22e7912e41a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\666af40445825b60b96d02b304bb7677\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\666af40445825b60b96d02b304bb7677\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\17388649ae338a23501118f801f51a80\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\17388649ae338a23501118f801f51a80\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\be49cfe65380b018d85519986ffa8500\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\be49cfe65380b018d85519986ffa8500\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\09de227f7928c32c57a964dd7c9104b4\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\09de227f7928c32c57a964dd7c9104b4\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\80a8ab1f9784ee28660c0b3e9538b6b6\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\80a8ab1f9784ee28660c0b3e9538b6b6\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\534cac120d403368e589e5d446a1df03\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\534cac120d403368e589e5d446a1df03\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4fde6db967e9edf68f940b6c7d49ecc1\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4fde6db967e9edf68f940b6c7d49ecc1\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\77782dfd18d860eaba833f2491071ee1\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\77782dfd18d860eaba833f2491071ee1\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b1ecffd73d5b4fe67a3b24cdd12d0789\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b1ecffd73d5b4fe67a3b24cdd12d0789\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b5ee2568eeb9cb53cd2b1f7913a14d20\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b5ee2568eeb9cb53cd2b1f7913a14d20\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\803cc0ed5a73bb3543061cde73284b06\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\803cc0ed5a73bb3543061cde73284b06\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52c791ee2ea6e83732c62768087b85ca\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\52c791ee2ea6e83732c62768087b85ca\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\602265030a5d2ede7b3df78d87cc45e6\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\602265030a5d2ede7b3df78d87cc45e6\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\91548cbd40b0dfa48c8f344b9b5c4376\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\91548cbd40b0dfa48c8f344b9b5c4376\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b0c87a9ac610e46df845b09d0617695\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1b0c87a9ac610e46df845b09d0617695\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7aabc0c58e181a57fddf1fefee3f041e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7aabc0c58e181a57fddf1fefee3f041e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed59826fbda97ece0491b02ad2a5c49d\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed59826fbda97ece0491b02ad2a5c49d\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\177e444593e4b7a8044efce760dac824\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\177e444593e4b7a8044efce760dac824\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3e96119c15d18892a7b6c93ef99f8ffa\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3e96119c15d18892a7b6c93ef99f8ffa\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cd89981e8e257c118ffb880ca7cb4ae3\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cd89981e8e257c118ffb880ca7cb4ae3\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\520ae8b17e80c30458dbdc30b8b89325\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\520ae8b17e80c30458dbdc30b8b89325\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6c992f49cd5dd6e821cb79fb6aed0059\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6c992f49cd5dd6e821cb79fb6aed0059\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d770120abe1e97d6594c17a45ee9bc5\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7d770120abe1e97d6594c17a45ee9bc5\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.noties.markwon:core:4.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\25c68a4d718f989d520ebcdf5aba741a\transformed\core-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.noties.markwon:core:4.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\25c68a4d718f989d520ebcdf5aba741a\transformed\core-4.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [me.zhanghai.android.appiconloader:appiconloader:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ab0066980fb3a4a2a408f5415fba5d1e\transformed\appiconloader-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [me.zhanghai.android.appiconloader:appiconloader:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ab0066980fb3a4a2a408f5415fba5d1e\transformed\appiconloader-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [me.zhanghai.android.appiconloader:appiconloader-iconloaderlib:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b24f98db99e9c5a3a4eb126660e4798c\transformed\appiconloader-iconloaderlib-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [me.zhanghai.android.appiconloader:appiconloader-iconloaderlib:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b24f98db99e9c5a3a4eb126660e4798c\transformed\appiconloader-iconloaderlib-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fda6ee821203d0ff67c9bee29fde0c8d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fda6ee821203d0ff67c9bee29fde0c8d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\56282bb99fd1c1c3e4b8a4f68b8b43dd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\56282bb99fd1c1c3e4b8a4f68b8b43dd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ea06cca3af6b852689a5c0ce647845a8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ea06cca3af6b852689a5c0ce647845a8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\de2e1a3f998b6450e20c48e5db4243cd\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\de2e1a3f998b6450e20c48e5db4243cd\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e66c1b829a791e58da9f30a58f9fd2c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e66c1b829a791e58da9f30a58f9fd2c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\14db14e8f77974cddd9a2c171ab5fc15\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\14db14e8f77974cddd9a2c171ab5fc15\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\48c9d3c93c0ff39da09afcf242af84ce\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\48c9d3c93c0ff39da09afcf242af84ce\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4bab32fe4f70edfeb1c454bf36b092fa\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4bab32fe4f70edfeb1c454bf36b092fa\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dac76183d0c97b1e012334ab33da5939\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dac76183d0c97b1e012334ab33da5939\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\06e6932f21585abcbc3515ea64448862\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\06e6932f21585abcbc3515ea64448862\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1d0e67c8877d8075be21723e49843c4e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1d0e67c8877d8075be21723e49843c4e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d1f7850da3e3bf76a65862abef43f684\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d1f7850da3e3bf76a65862abef43f684\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.topjohnwu.libsu:service:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0aa38518544eca4718361df7fd0b0571\transformed\service-6.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.topjohnwu.libsu:service:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0aa38518544eca4718361df7fd0b0571\transformed\service-6.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.topjohnwu.libsu:io:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5ebf347b78b43198723a272c7654512d\transformed\io-6.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.topjohnwu.libsu:io:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5ebf347b78b43198723a272c7654512d\transformed\io-6.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.topjohnwu.libsu:core:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\12384b829e3c3ee115d21e992b3f035a\transformed\core-6.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.topjohnwu.libsu:core:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\12384b829e3c3ee115d21e992b3f035a\transformed\core-6.0.0\AndroidManifest.xml:5:5-44
MERGED from [dev.rikka.rikkax.parcelablelist:parcelablelist:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\538d41cd23daafbb8879ca4f42f2f664\transformed\parcelablelist-2.0.1-debug\AndroidManifest.xml:5:5-7:41
MERGED from [dev.rikka.rikkax.parcelablelist:parcelablelist:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\538d41cd23daafbb8879ca4f42f2f664\transformed\parcelablelist-2.0.1-debug\AndroidManifest.xml:5:5-7:41
MERGED from [org.lsposed.libcxx:libcxx:28.1.13356709] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\290d87afc4f3d4d5661561da4a4f63ee\transformed\libcxx-28.1.13356709\AndroidManifest.xml:5:5-44
MERGED from [org.lsposed.libcxx:libcxx:28.1.13356709] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\290d87afc4f3d4d5661561da4a4f63ee\transformed\libcxx-28.1.13356709\AndroidManifest.xml:5:5-44
MERGED from [com.github.topjohnwu.libsu:nio:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fee123e0e31195b9dd319e38b0414583\transformed\nio-6.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.topjohnwu.libsu:nio:6.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fee123e0e31195b9dd319e38b0414583\transformed\nio-6.0.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\KernelSU-Next-1.0.9\manager\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9c80829a381bd970006d235b61d1da36\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e66c1b829a791e58da9f30a58f9fd2c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e66c1b829a791e58da9f30a58f9fd2c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fd751eb121db7bf2c32ae9d8268dce16\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\90c11081a30ff56954ded108ca563a62\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.rifsxd.ksunext.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.rifsxd.ksunext.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\155e6c12dc97cf97c948c578b691358c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fb3b01d40b11e2eb27556df6aa80d897\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\20a3fb36bfc64b6ad665b21b02b0c23f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
