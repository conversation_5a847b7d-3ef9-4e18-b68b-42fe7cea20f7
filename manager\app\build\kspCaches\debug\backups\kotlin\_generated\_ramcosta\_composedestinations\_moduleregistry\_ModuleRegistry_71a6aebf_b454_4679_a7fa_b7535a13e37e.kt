package _generated._ramcosta._composedestinations._moduleregistry

import com.ramcosta.composedestinations.spec.DestinationSpec

public annotation class _Info_71a6aebf_b454_4679_a7fa_b7535a13e37e(
    val moduleName: String,
    val packageName: String,
    val hasNavArgsPackage: Boolean,
    val typeResults: Array<_Destination_Result_Info_71a6aebf_b454_4679_a7fa_b7535a13e37e> = emptyArray(),
    val topLevelGraphs: Array<String> = emptyArray()
)

public annotation class _Destination_Result_Info_71a6aebf_b454_4679_a7fa_b7535a13e37e(
    val destination: String,
    val resultType: String,
    val resultNavType: String,
    val isResultNullable: Boolean
)

@_Info_71a6aebf_b454_4679_a7fa_b7535a13e37e(
    moduleName = "",
    packageName = "com.ramcosta.composedestinations.generated",
    hasNavArgsPackage = false,
    typeResults = [

    ],
    topLevelGraphs = [
		"RootNavGraph"
    ]
)
public object _ModuleRegistry_71a6aebf_b454_4679_a7fa_b7535a13e37e