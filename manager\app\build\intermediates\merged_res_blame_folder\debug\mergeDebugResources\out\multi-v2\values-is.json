{"logs": [{"outputFile": "com.rifsxd.ksunext.app-mergeDebugResources-77:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9e12252ef6834a61ce47908bfdf2c710\\transformed\\material-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "136", "startColumns": "4", "startOffsets": "13061", "endColumns": "90", "endOffsets": "13147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\a64fe05c0979e98a37ab829c2d67ec24\\transformed\\core-1.3.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,100", "endColumns": "44,33", "endOffsets": "95,129"}, "to": {"startLines": "46,184", "startColumns": "4,4", "startOffsets": "4184,16927", "endColumns": "44,33", "endOffsets": "4224,16956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ed59826fbda97ece0491b02ad2a5c49d\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,283,364,463,562,647,727,822,911,993,1071,1154,1224,1311,1386,1461,1535,1612,1680", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,86,74,74,73,76,67,119", "endOffsets": "278,359,458,557,642,722,817,906,988,1066,1149,1219,1306,1381,1456,1530,1607,1675,1795"}, "to": {"startLines": "50,51,53,54,55,63,64,182,183,186,187,193,195,196,197,198,200,201,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4523,4614,4799,4898,4997,5665,5745,16756,16845,17041,17119,17674,17819,17906,17981,18056,18231,18308,18376", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,86,74,74,73,76,67,119", "endOffsets": "4609,4690,4893,4992,5077,5740,5835,16840,16922,17114,17197,17739,17901,17976,18051,18125,18303,18371,18491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c94e4a086de3d3c71b4c9b9ecd123998\\transformed\\appcompat-1.7.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,420,517,629,714,815,929,1010,1089,1180,1273,1366,1460,1566,1659,1754,1849,1940,2034,2115,2225,2332,2429,2538,2638,2741,2896,17430", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "415,512,624,709,810,924,1005,1084,1175,1268,1361,1455,1561,1654,1749,1844,1935,2029,2110,2220,2327,2424,2533,2633,2736,2891,2989,17506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\b0c018149e18020465f8106101906127\\transformed\\browser-1.8.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "52,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "4695,5293,5394,5500", "endColumns": "103,100,105,100", "endOffsets": "4794,5389,5495,5596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\4a70d17a9618e65af9cd54de947c82cb\\transformed\\list-1.3.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,119", "endOffsets": "158,278"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "17202,17310", "endColumns": "107,119", "endOffsets": "17305,17425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ea4b31e0f2565dabf3acdd6c35348c38\\transformed\\material-1.12.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1009,1074,1162,1226,1287,1377,1441,1504,1566,1634,1698,1754,1877,1942,2004,2060,2131,2258,2342,2416,2513,2594,2678,2814,2891,2968,3084,3171,3250,3307,3362,3428,3504,3584,3655,3731,3798,3872,3942,4008,4110,4196,4266,4357,4447,4521,4594,4683,4734,4815,4887,4968,5054,5116,5180,5243,5312,5426,5532,5640,5742,5803,5862,5942,6026,6105", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79,83,78,74", "endOffsets": "265,339,411,490,572,652,749,864,946,1004,1069,1157,1221,1282,1372,1436,1499,1561,1629,1693,1749,1872,1937,1999,2055,2126,2253,2337,2411,2508,2589,2673,2809,2886,2963,3079,3166,3245,3302,3357,3423,3499,3579,3650,3726,3793,3867,3937,4003,4105,4191,4261,4352,4442,4516,4589,4678,4729,4810,4882,4963,5049,5111,5175,5238,5307,5421,5527,5635,5737,5798,5857,5937,6021,6100,6175"}, "to": {"startLines": "2,34,35,36,37,38,47,48,49,56,57,58,62,65,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,185,191,192,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3080,3154,3226,3305,3387,4229,4326,4441,5082,5140,5205,5601,5840,12090,12180,12244,12307,12369,12437,12501,12557,12680,12745,12807,12863,12934,13152,13236,13310,13407,13488,13572,13708,13785,13862,13978,14065,14144,14201,14256,14322,14398,14478,14549,14625,14692,14766,14836,14902,15004,15090,15160,15251,15341,15415,15488,15577,15628,15709,15781,15862,15948,16010,16074,16137,16206,16320,16426,16534,16636,16697,16961,17511,17595,17744", "endLines": "5,34,35,36,37,38,47,48,49,56,57,58,62,65,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,185,191,192,194", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79,83,78,74", "endOffsets": "315,3149,3221,3300,3382,3462,4321,4436,4518,5135,5200,5288,5660,5896,12175,12239,12302,12364,12432,12496,12552,12675,12740,12802,12858,12929,13056,13231,13305,13402,13483,13567,13703,13780,13857,13973,14060,14139,14196,14251,14317,14393,14473,14544,14620,14687,14761,14831,14897,14999,15085,15155,15246,15336,15410,15483,15572,15623,15704,15776,15857,15943,16005,16069,16132,16201,16315,16421,16529,16631,16692,16751,17036,17590,17669,17814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\baf1b711915162474bbf875d9a8baf28\\transformed\\foundation-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,228", "endColumns": "85,86,86", "endOffsets": "136,223,310"}, "to": {"startLines": "33,203,204", "startColumns": "4,4,4", "startOffsets": "2994,18496,18583", "endColumns": "85,86,86", "endOffsets": "3075,18578,18665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\155e6c12dc97cf97c948c578b691358c\\transformed\\core-1.16.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "39,40,41,42,43,44,45,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3467,3562,3669,3766,3866,3969,4073,18130", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3557,3664,3761,3861,3964,4068,4179,18226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9d1fcfacc0f20874a2a81b41ebfa5291\\transformed\\material3-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,500,597,696,812,953,1080,1215,1305,1406,1503,1603,1718,1844,1950,2075,2199,2341,2512,2635,2751,2870,2992,3090,3188,3297,3419,3525,3633,3736,3866,4001,4109,4214,4290,4384,4477,4591,4676,4761,4870,4950,5041,5142,5243,5338,5446,5534,5639,5740,5846,5966,6046,6148", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "164,276,383,495,592,691,807,948,1075,1210,1300,1401,1498,1598,1713,1839,1945,2070,2194,2336,2507,2630,2746,2865,2987,3085,3183,3292,3414,3520,3628,3731,3861,3996,4104,4209,4285,4379,4472,4586,4671,4756,4865,4945,5036,5137,5238,5333,5441,5529,5634,5735,5841,5961,6041,6143,6239"}, "to": {"startLines": "66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5901,6015,6127,6234,6346,6443,6542,6658,6799,6926,7061,7151,7252,7349,7449,7564,7690,7796,7921,8045,8187,8358,8481,8597,8716,8838,8936,9034,9143,9265,9371,9479,9582,9712,9847,9955,10060,10136,10230,10323,10437,10522,10607,10716,10796,10887,10988,11089,11184,11292,11380,11485,11586,11692,11812,11892,11994", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "6010,6122,6229,6341,6438,6537,6653,6794,6921,7056,7146,7247,7344,7444,7559,7685,7791,7916,8040,8182,8353,8476,8592,8711,8833,8931,9029,9138,9260,9366,9474,9577,9707,9842,9950,10055,10131,10225,10318,10432,10517,10602,10711,10791,10882,10983,11084,11179,11287,11375,11480,11581,11687,11807,11887,11989,12085"}}]}]}