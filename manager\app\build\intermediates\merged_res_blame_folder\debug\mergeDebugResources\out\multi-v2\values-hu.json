{"logs": [{"outputFile": "com.rifsxd.ksunext.app-mergeDebugResources-77:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\b0c018149e18020465f8106101906127\\transformed\\browser-1.8.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "52,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "4806,5411,5512,5627", "endColumns": "95,100,114,103", "endOffsets": "4897,5507,5622,5726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\baf1b711915162474bbf875d9a8baf28\\transformed\\foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,231", "endColumns": "86,88,96", "endOffsets": "137,226,323"}, "to": {"startLines": "33,203,204", "startColumns": "4,4,4", "startOffsets": "3067,18903,18992", "endColumns": "86,88,96", "endOffsets": "3149,18987,19084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ed59826fbda97ece0491b02ad2a5c49d\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,288,376,473,572,659,741,837,926,1013,1096,1184,1258,1351,1426,1497,1567,1646,1712", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,92,74,70,69,78,65,120", "endOffsets": "283,371,468,567,654,736,832,921,1008,1091,1179,1253,1346,1421,1492,1562,1641,1707,1828"}, "to": {"startLines": "50,51,53,54,55,63,64,182,183,186,187,193,195,196,197,198,200,201,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4623,4718,4902,4999,5098,5806,5888,17141,17230,17436,17519,18080,18227,18320,18395,18466,18637,18716,18782", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,92,74,70,69,78,65,120", "endOffsets": "4713,4801,4994,5093,5180,5883,5979,17225,17312,17514,17602,18149,18315,18390,18461,18531,18711,18777,18898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\155e6c12dc97cf97c948c578b691358c\\transformed\\core-1.16.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "39,40,41,42,43,44,45,199", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3558,3655,3757,3859,3960,4063,4170,18536", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3650,3752,3854,3955,4058,4165,4275,18632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\a64fe05c0979e98a37ab829c2d67ec24\\transformed\\core-1.3.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,96", "endColumns": "40,33", "endOffsets": "91,125"}, "to": {"startLines": "46,184", "startColumns": "4,4", "startOffsets": "4280,17317", "endColumns": "40,33", "endOffsets": "4316,17346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9e12252ef6834a61ce47908bfdf2c710\\transformed\\material-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "96", "endOffsets": "147"}, "to": {"startLines": "136", "startColumns": "4", "startOffsets": "13336", "endColumns": "96", "endOffsets": "13428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\9d1fcfacc0f20874a2a81b41ebfa5291\\transformed\\material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4731,4819,4906,5007,5088,5171,5270,5376,5471,5574,5660,5769,5867,5973,6094,6175,6287", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4726,4814,4901,5002,5083,5166,5265,5371,5466,5569,5655,5764,5862,5968,6089,6170,6282,6380"}, "to": {"startLines": "66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6043,6164,6280,6388,6504,6599,6696,6810,6950,7073,7220,7305,7405,7503,7605,7727,7864,7969,8109,8247,8373,8569,8692,8814,8936,9062,9161,9256,9375,9512,9614,9725,9829,9974,10121,10228,10335,10419,10517,10611,10719,10807,10894,10995,11076,11159,11258,11364,11459,11562,11648,11757,11855,11961,12082,12163,12275", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "6159,6275,6383,6499,6594,6691,6805,6945,7068,7215,7300,7400,7498,7600,7722,7859,7964,8104,8242,8368,8564,8687,8809,8931,9057,9156,9251,9370,9507,9609,9720,9824,9969,10116,10223,10330,10414,10512,10606,10714,10802,10889,10990,11071,11154,11253,11359,11454,11557,11643,11752,11850,11956,12077,12158,12270,12368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\ea4b31e0f2565dabf3acdd6c35348c38\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "2,34,35,36,37,38,47,48,49,56,57,58,62,65,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,185,191,192,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3154,3235,3311,3388,3478,4321,4420,4540,5185,5248,5312,5731,5984,12373,12483,12545,12614,12672,12744,12805,12860,12963,13020,13080,13135,13216,13433,13516,13594,13690,13776,13864,13999,14082,14162,14302,14396,14478,14531,14582,14648,14724,14806,14877,14961,15038,15113,15192,15269,15374,15470,15547,15639,15736,15810,15895,15992,16044,16127,16194,16282,16369,16431,16495,16558,16624,16722,16828,16922,17029,17086,17351,17918,18003,18154", "endLines": "5,34,35,36,37,38,47,48,49,56,57,58,62,65,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,185,191,192,194", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "308,3230,3306,3383,3473,3553,4415,4535,4618,5243,5307,5406,5801,6038,12478,12540,12609,12667,12739,12800,12855,12958,13015,13075,13130,13211,13331,13511,13589,13685,13771,13859,13994,14077,14157,14297,14391,14473,14526,14577,14643,14719,14801,14872,14956,15033,15108,15187,15264,15369,15465,15542,15634,15731,15805,15890,15987,16039,16122,16189,16277,16364,16426,16490,16553,16619,16717,16823,16917,17024,17081,17136,17431,17998,18075,18222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c94e4a086de3d3c71b4c9b9ecd123998\\transformed\\appcompat-1.7.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,17834", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,17913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\4a70d17a9618e65af9cd54de947c82cb\\transformed\\list-1.3.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,112", "endOffsets": "164,277"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "17607,17721", "endColumns": "113,112", "endOffsets": "17716,17829"}}]}]}