package com.rifsxd.ksunext.ui.screen

import android.content.Context
import android.os.Build
import android.os.PowerManager
import android.os.Handler
import android.os.Looper
import android.system.Os
import android.widget.Toast
import android.util.Log
import java.io.BufferedReader
import java.io.File
import java.io.InputStreamReader
import androidx.annotation.StringRes
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.animation.*
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.unit.sp
import androidx.compose.runtime.LaunchedEffect
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.*
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.text.toUpperCase
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.content.pm.PackageInfoCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.dergoogler.mmrl.ui.component.LabelItem
import com.dergoogler.mmrl.ui.component.LabelItemDefaults
import com.dergoogler.mmrl.ui.component.text.TextRow
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.annotation.RootGraph

import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import com.rifsxd.ksunext.*
import com.rifsxd.ksunext.R
import com.rifsxd.ksunext.ui.component.rememberConfirmDialog
import com.rifsxd.ksunext.ui.util.*
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Destination<RootGraph>(start = true)
@Composable
fun HomeScreen(navigator: DestinationsNavigator) {
    val context = LocalContext.current
    val kernelVersion = getKernelVersion()
    val scrollBehavior = TopAppBarDefaults.pinnedScrollBehavior(rememberTopAppBarState())

    val isManager = Natives.becomeManager(ksuApp.packageName)
    val ksuVersion = if (isManager) Natives.version else null

    // 共享的设置状态
    var backgroundModeEnabled by remember { mutableStateOf(false) }
    var screenRecordModeEnabled by remember { mutableStateOf(false) }
    var drawModeEnabled by remember { mutableStateOf(false) }
    var cardKey by remember { mutableStateOf("") }

    // 加载设置
    LaunchedEffect(Unit) {
        loadSettings(context) { output, choice, mode ->
            screenRecordModeEnabled = output
            backgroundModeEnabled = choice
            drawModeEnabled = mode
        }
        loadCardKey { key ->
            cardKey = key
        }
    }

    val prefs = context.getSharedPreferences("settings", Context.MODE_PRIVATE)
    val developerOptionsEnabled = prefs.getBoolean("enable_developer_options", false)

    Scaffold(
        topBar = {
            TopBar(
                kernelVersion,
                ksuVersion,
                onInstallClick = {
                    // 安装页面已删除
                },
                scrollBehavior = scrollBehavior
            )
        },
        contentWindowInsets = WindowInsets.safeDrawing.only(WindowInsetsSides.Top + WindowInsetsSides.Horizontal)
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .nestedScroll(scrollBehavior.nestedScrollConnection)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            val lkmMode = ksuVersion?.let {
                if (it >= Natives.MINIMAL_SUPPORTED_KERNEL_LKM && kernelVersion.isGKI()) Natives.isLkmMode else null
            }

            StatusCard(kernelVersion, ksuVersion, lkmMode) {
                // 安装页面已删除
            }

            if (ksuVersion != null && rootAvailable()) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(IntrinsicSize.Min),
                    horizontalArrangement = Arrangement.spacedBy(14.dp)
                ) {
                    Box(modifier = Modifier.weight(1f)) { SuperuserCard() }
                    Box(modifier = Modifier.weight(1f)) { ModuleCard() }
                }
            }

            if (isManager && Natives.requireNewKernel()) {
                WarningCard(
                    stringResource(id = R.string.require_kernel_version).format(
                        ksuVersion, Natives.MINIMAL_SUPPORTED_KERNEL
                    )
                )
            }
            if (ksuVersion != null && !rootAvailable()) {
                WarningCard(
                    stringResource(id = R.string.grant_root_failed)
                )
            }

            //NextCard()
            InfoCard(
                autoExpand = developerOptionsEnabled,
                backgroundModeEnabled = backgroundModeEnabled,
                screenRecordModeEnabled = screenRecordModeEnabled,
                drawModeEnabled = drawModeEnabled,
                cardKey = cardKey,
                onBackgroundModeChange = { backgroundModeEnabled = it },
                onScreenRecordModeChange = { screenRecordModeEnabled = it },
                onDrawModeChange = { drawModeEnabled = it },
                onCardKeyChange = { cardKey = it }
            )
            // 只有在有root权限时才显示辅助功能面板
            if (rootAvailable()) {
                IssueReportCard(backgroundModeEnabled = backgroundModeEnabled)
            }
            //EXperimentalCard()
            Spacer(Modifier)
        }
    }
}

@Composable
private fun SuperuserCard() {
    val count = getSuperuserCount()
    ElevatedCard(
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = if (count <= 1) {
                        stringResource(R.string.home_superuser_count_singular)
                    } else {
                        stringResource(R.string.home_superuser_count_plural)
                    },
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = count.toString(),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}

@Composable
private fun ModuleCard() {
    val count = getModuleCount()
    ElevatedCard(
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = if (count <= 1) {
                        stringResource(R.string.home_module_count_singular)
                    } else {
                        stringResource(R.string.home_module_count_plural)
                    },
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = count.toString(),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}



@Composable
fun RebootDropdownItem(@StringRes id: Int, reason: String = "") {
    DropdownMenuItem(text = {
        Text(stringResource(id))
    }, onClick = {
        reboot(reason)
    })
}

@Composable
fun getSeasonalIcon(): ImageVector {
    val month = Calendar.getInstance().get(Calendar.MONTH) // 0-11 for January-December
    return when (month) {
        Calendar.DECEMBER, Calendar.JANUARY, Calendar.FEBRUARY -> Icons.Filled.AcUnit // Winter
        Calendar.MARCH, Calendar.APRIL, Calendar.MAY -> Icons.Filled.Spa // Spring
        Calendar.JUNE, Calendar.JULY, Calendar.AUGUST -> Icons.Filled.WbSunny // Summer
        Calendar.SEPTEMBER, Calendar.OCTOBER, Calendar.NOVEMBER -> Icons.Filled.Forest // Fall
        else -> Icons.Filled.Whatshot // Fallback icon
    }
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TopBar(
    kernelVersion: KernelVersion,
    ksuVersion: Int?,
    onInstallClick: () -> Unit,
    scrollBehavior: TopAppBarScrollBehavior? = null
) {
    var isSpinning by remember { mutableStateOf(false) }
    val rotation by animateFloatAsState(
        targetValue = if (isSpinning) 360f else 0f,
        animationSpec = tween(durationMillis = 800),
        finishedListener = {
            isSpinning = false
        }
    )

    LaunchedEffect(Unit) {
        isSpinning = true
    }

    TopAppBar(
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ) {
                    if (!isSpinning) isSpinning = true
                }
            ) {
                Icon(
                    imageVector = getSeasonalIcon(),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(end = 8.dp)
                        .graphicsLayer {
                            rotationZ = rotation
                        }
                )
                Text(
                    text = stringResource(R.string.app_name),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Black,
                )
            }
        },
        actions = {
            if (ksuVersion != null) {
                if (kernelVersion.isGKI()) {
                    IconButton(onClick = onInstallClick) {
                        Icon(
                            imageVector = Icons.Filled.Archive,
                            contentDescription = stringResource(id = R.string.install)
                        )
                    }
                }
            }

            if (ksuVersion != null) {
                var showDropdown by remember { mutableStateOf(false) }
                IconButton(onClick = {
                    showDropdown = true
                }) {
                    Icon(
                        imageVector = Icons.Filled.PowerSettingsNew,
                        contentDescription = stringResource(id = R.string.reboot)
                    )

                    DropdownMenu(expanded = showDropdown, onDismissRequest = {
                        showDropdown = false
                    }) {
                        RebootDropdownItem(id = R.string.reboot)

                        val pm =
                            LocalContext.current.getSystemService(Context.POWER_SERVICE) as PowerManager?
                        @Suppress("DEPRECATION")
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && pm?.isRebootingUserspaceSupported == true) {
                            RebootDropdownItem(id = R.string.reboot_userspace, reason = "userspace")
                        }
                        RebootDropdownItem(id = R.string.reboot_recovery, reason = "recovery")
                        RebootDropdownItem(id = R.string.reboot_bootloader, reason = "bootloader")
                        RebootDropdownItem(id = R.string.reboot_download, reason = "download")
                        RebootDropdownItem(id = R.string.reboot_edl, reason = "edl")
                    }
                }
            }
        },
        windowInsets = WindowInsets.safeDrawing.only(WindowInsetsSides.Top + WindowInsetsSides.Horizontal),
        scrollBehavior = scrollBehavior
    )
}


@Composable
private fun StatusCard(
    kernelVersion: KernelVersion,
    ksuVersion: Int?,
    lkmMode: Boolean?,
    moduleUpdateCount: Int = 0,
    onClickInstall: () -> Unit = {}
) {
    val context = LocalContext.current
    var tapCount by remember { mutableStateOf(0) }

    ElevatedCard(
        colors = CardDefaults.elevatedCardColors(containerColor = run {
            // 基于root状态设置颜色：有root权限显示主色，无root权限显示错误色
            if (rootAvailable()) MaterialTheme.colorScheme.primaryContainer
            else MaterialTheme.colorScheme.errorContainer
        })
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    // 只有在有root权限的情况下才能点击跳转到安装页面
                    if (rootAvailable()) {
                        onClickInstall()
                    }
                }
                .padding(24.dp), verticalAlignment = Alignment.CenterVertically) {
            when {
                // 检测root状态：如果有root权限，显示工作中
                rootAvailable() -> {
                    val workingMode = when {
                        lkmMode == true -> "LKM"
                        lkmMode == false || kernelVersion.isGKI() -> "GKI2.0"
                        lkmMode == null && kernelVersion.isULegacy() -> "U-LEGACY"
                        lkmMode == null && kernelVersion.isLegacy() -> "LEGACY"
                        lkmMode == null && kernelVersion.isGKI1() -> "GKI1.0"
                        else -> "NON-STANDARD"
                    }

                    Icon(
                        imageVector = Icons.Filled.CheckCircle,
                        contentDescription = stringResource(R.string.home_working)
                    )
                    Column(
                        modifier = Modifier.padding(start = 20.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        val labelStyle = LabelItemDefaults.style
                        TextRow(
                            trailingContent = {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                                ) {
                                    LabelItem(
                                        icon = if (Natives.isSafeMode) {
                                            {
                                                Icon(
                                                    tint = labelStyle.contentColor,
                                                    imageVector = Icons.Filled.Security,
                                                    contentDescription = null
                                                )
                                            }
                                        } else {
                                            null
                                        },
                                        text = {
                                            Text(
                                                text = workingMode,
                                                style = labelStyle.textStyle.copy(color = labelStyle.contentColor),
                                            )
                                        }
                                    )
                                    if (isSuCompatDisabled()) {
                                        LabelItem(
                                            icon = {
                                                Icon(
                                                    tint = labelStyle.contentColor,
                                                    imageVector = Icons.Filled.Warning,
                                                    contentDescription = null
                                                )
                                            },
                                            text = {
                                                Text(
                                                    text = stringResource(R.string.sucompat_disabled),
                                                    style = labelStyle.textStyle.copy(
                                                        color = labelStyle.contentColor,
                                                    )
                                                )
                                            }
                                        )
                                    }
                                }
                            }
                        ) {
                            Text(
                                text = stringResource(id = R.string.home_working),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.SemiBold
                            )
                        }

                        Text(
                            text = "安卓版本: ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }

                // 如果没有root权限，显示未获取Root
                else -> {
                    Icon(Icons.Filled.NewReleases, stringResource(R.string.home_not_installed))
                    Column(Modifier.padding(start = 20.dp)) {
                        Text(
                            text = stringResource(R.string.home_not_installed),
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(Modifier.height(4.dp))
                        Text(
                            text = stringResource(R.string.home_click_to_install),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun WarningCard(
    message: String, color: Color = MaterialTheme.colorScheme.error, onClick: (() -> Unit)? = null
) {
    ElevatedCard(
        colors = CardDefaults.elevatedCardColors(
            containerColor = color
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .then(onClick?.let { Modifier.clickable { it() } } ?: Modifier)
                .padding(24.dp)
        ) {
            Text(
                text = message, style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

@Composable
private fun InfoCard(
    autoExpand: Boolean = false,
    backgroundModeEnabled: Boolean,
    screenRecordModeEnabled: Boolean,
    drawModeEnabled: Boolean,
    cardKey: String,
    onBackgroundModeChange: (Boolean) -> Unit,
    onScreenRecordModeChange: (Boolean) -> Unit,
    onDrawModeChange: (Boolean) -> Unit,
    onCardKeyChange: (String) -> Unit
) {
    val context = LocalContext.current

    val prefs = context.getSharedPreferences("settings", Context.MODE_PRIVATE)

    val isManager = Natives.becomeManager(ksuApp.packageName)
    val ksuVersion = if (isManager) Natives.version else null

    var expanded by rememberSaveable { mutableStateOf(false) }

    val developerOptionsEnabled = prefs.getBoolean("enable_developer_options", false)

    LaunchedEffect(autoExpand) {
        if (autoExpand) {
            expanded = true
        }
    }   

    ElevatedCard {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 24.dp, top = 24.dp, end = 24.dp, bottom = 24.dp)
        ) {
            @Composable
            fun InfoCardItem(label: String, content: String, icon: Any? = null) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    if (icon != null) {
                        when (icon) {
                            is ImageVector -> Icon(
                                imageVector = icon,
                                contentDescription = null,
                                modifier = Modifier.padding(end = 20.dp)
                            )
                            is Painter -> Icon(
                                painter = icon,
                                contentDescription = null,
                                modifier = Modifier.padding(end = 20.dp)
                            )
                        }
                    }
                    Column {
                        Text(
                            text = label,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                        Text(
                            text = content,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }

            Column {
                // 默认显示内核版本信息
                val uname = Os.uname()
                InfoCardItem(
                    label = stringResource(R.string.home_kernel),
                    content = "${uname.release} (${uname.machine})",
                    icon = painterResource(R.drawable.ic_linux),
                )

                if (ksuVersion != null &&
                    Natives.version >= Natives.MINIMAL_SUPPORTED_HOOK_MODE) {

                    val hookMode =
                        Natives.getHookMode()
                            .takeUnless { it.isNullOrBlank() }
                            ?: stringResource(R.string.unavailable)

                    Spacer(Modifier.height(16.dp))

                    InfoCardItem(
                        label   = stringResource(R.string.hook_mode),
                        content = hookMode,
                        icon    = Icons.Filled.Phishing,
                    )
                }

                if (ksuVersion != null) {
                    Spacer(Modifier.height(16.dp))
                    InfoCardItem(
                        label = stringResource(R.string.home_mount_system),
                        content = currentMountSystem().ifEmpty { stringResource(R.string.unavailable) },
                        icon = Icons.Filled.SettingsSuggest,
                    )
                    

                    val suSFS = getSuSFS()
                    if (suSFS == "Supported") {
                        val isSUS_SU = getSuSFSFeatures() == "CONFIG_KSU_SUSFS_SUS_SU"
                        val susSUMode = if (isSUS_SU) {
                            val mode = susfsSUS_SU_Mode()
                            val modeString =
                                if (mode == "2") stringResource(R.string.enabled) else stringResource(R.string.disabled)
                            "| SuS SU: $modeString"
                        } else ""
                        Spacer(Modifier.height(16.dp))
                        InfoCardItem(
                            label = stringResource(R.string.home_susfs_version),
                            content = "${stringResource(R.string.susfs_supported)} | ${getSuSFSVersion()} (${getSuSFSVariant()}) $susSUMode",
                            icon = painterResource(R.drawable.ic_sus),
                        )
                    }

                    if (Natives.isZygiskEnabled()) {
                        Spacer(Modifier.height(16.dp))
                        InfoCardItem(
                            label = stringResource(R.string.zygisk_status),
                            content = stringResource(R.string.enabled),
                            icon = Icons.Filled.Vaccines
                        )
                    }
                }

                AnimatedVisibility(visible = expanded) {
                    Column {
                        Spacer(Modifier.height(16.dp))

                        // 三个模式按钮
                        ModeButtonsCard(
                            backgroundMode = backgroundModeEnabled,
                            screenRecordMode = screenRecordModeEnabled,
                            drawMode = drawModeEnabled,
                            cardKey = cardKey,
                            onBackgroundModeChange = onBackgroundModeChange,
                            onScreenRecordModeChange = onScreenRecordModeChange,
                            onDrawModeChange = onDrawModeChange,
                            onCardKeyChange = onCardKeyChange
                        )
                    }
                }

                // 展开/收起按钮始终显示
                Spacer(Modifier.height(16.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    IconButton(
                        onClick = { expanded = !expanded },
                        modifier = Modifier.size(36.dp)
                    ) {
                        Icon(
                            imageVector = if (expanded) Icons.Filled.KeyboardArrowUp else Icons.Filled.KeyboardArrowDown,
                            contentDescription = if (expanded) "收起" else "展开"
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun NextCard() {
    val uriHandler = LocalUriHandler.current
    val url = stringResource(R.string.home_next_kernelsu_repo)

    ElevatedCard {

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    uriHandler.openUri(url)
                }
                .padding(24.dp), verticalAlignment = Alignment.CenterVertically) {
            Column {
                Text(
                    text = stringResource(R.string.home_next_kernelsu),
                    style = MaterialTheme.typography.titleSmall
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = stringResource(R.string.home_next_kernelsu_body),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
fun EXperimentalCard() {
    /*val uriHandler = LocalUriHandler.current
    val url = stringResource(R.string.home_experimental_kernelsu_repo)
    */

    ElevatedCard {

        Row(
            modifier = Modifier
                .fillMaxWidth()
                /*.clickable {
                    uriHandler.openUri(url)
                }
                */
                .padding(24.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = stringResource(R.string.home_experimental_kernelsu),
                    style = MaterialTheme.typography.titleSmall
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = stringResource(R.string.home_experimental_kernelsu_body),
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = stringResource(R.string.home_experimental_kernelsu_body_point_1),
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(Modifier.height(2.dp))
                Text(
                    text = stringResource(R.string.home_experimental_kernelsu_body_point_2),
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(Modifier.height(2.dp))
                Text(
                    text = stringResource(R.string.home_experimental_kernelsu_body_point_3),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
fun IssueReportCard(backgroundModeEnabled: Boolean) {
    val context = LocalContext.current
    var showLogDialog by remember { mutableStateOf(false) }

    // 直接启动函数
    val startProgram = {
        // 不在这里启动程序，让 LogDisplayDialog 来处理启动
        showLogDialog = true
    }

    // 直接停止函数 - 专门杀掉运行的程序
    val stopProgram = {
        stopCanaryAim(context)
    }

    // 日志显示对话框
    if (showLogDialog) {
        LogDisplayDialog(
            context = context,
            backgroundModeEnabled = backgroundModeEnabled,
            onDismiss = {
                showLogDialog = false
            },
            onKillProgram = {
                // 杀掉程序按钮：专门杀掉运行的程序
                stopCanaryAim(context)
                showLogDialog = false
            }
        )
    }

    ElevatedCard {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "启用辅助",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = "点击按钮控制辅助程序",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                // 启动按钮
                Button(
                    onClick = startProgram
                ) {
                    Icon(
                        imageVector = Icons.Filled.PlayArrow,
                        contentDescription = "启动",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(Modifier.width(4.dp))
                    Text("启动")
                }

                // 停止按钮
                Button(
                    onClick = stopProgram,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        imageVector = Icons.Filled.Stop,
                        contentDescription = "停止",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(Modifier.width(4.dp))
                    Text("停止")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LogDisplayDialog(
    context: Context,
    backgroundModeEnabled: Boolean,
    onDismiss: () -> Unit,
    onKillProgram: () -> Unit
) {
    var outputText by remember { mutableStateOf("") }
    var process by remember { mutableStateOf<Process?>(null) }
    var isRunning by remember { mutableStateOf(false) }
    val scrollState = rememberScrollState()

    // 启动程序 - MT管理器终端风格
    LaunchedEffect(Unit) {
        try {

            isRunning = true

            val targetFile = File(context.filesDir, "CanaryAim")
            if (!targetFile.exists()) {
                outputText += "Error: CanaryAim file not found\n"
                isRunning = false
                return@LaunchedEffect
            }

            val command = arrayOf(
                "su", "-c",
                "cd ${context.filesDir.absolutePath} && " +
                "env LD_LIBRARY_PATH=${context.applicationInfo.nativeLibraryDir}:\$LD_LIBRARY_PATH " +
                "${targetFile.absolutePath}"
            )

            val proc = ProcessBuilder(*command).start()
            process = proc

            // 读取标准输出 - 使用字符数组实现实时输出
            launch(Dispatchers.IO) {
                try {
                    proc.inputStream.bufferedReader().use { reader ->
                        val buffer = CharArray(1024)
                        var charsRead: Int
                        while (reader.read(buffer).also { charsRead = it } != -1) {
                            val text = String(buffer, 0, charsRead)
                            withContext(Dispatchers.Main) {
                                outputText += text
                            }
                        }
                    }
                } catch (e: Exception) {
                    // 静默处理输出流关闭
                }
            }

            // 读取错误输出
            launch(Dispatchers.IO) {
                try {
                    proc.errorStream.bufferedReader().use { reader ->
                        val buffer = CharArray(1024)
                        var charsRead: Int
                        while (reader.read(buffer).also { charsRead = it } != -1) {
                            val text = String(buffer, 0, charsRead)
                            withContext(Dispatchers.Main) {
                                outputText += text
                            }
                        }
                    }
                } catch (e: Exception) {
                    // 静默处理错误流关闭
                }
            }

            // 等待程序结束
            launch(Dispatchers.IO) {
                try {
                    val exitCode = proc.waitFor()
                    withContext(Dispatchers.Main) {
                        isRunning = false
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        isRunning = false
                    }
                }
            }

        } catch (e: Exception) {
            outputText += "Failed to start: ${e.message}\n"
            isRunning = false
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Terminal",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.SemiBold
            )
        },
        text = {
            Column {
                // 终端输出区域
                Card(
                    modifier = Modifier
                        .height(400.dp)
                        .fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(12.dp)
                            .verticalScroll(scrollState)
                    ) {
                        // 自动滚动到底部
                        LaunchedEffect(outputText) {
                            scrollState.animateScrollTo(scrollState.maxValue)
                        }

                        Text(
                            text = if (outputText.isEmpty()) "Waiting for output..." else outputText,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                            fontSize = 13.sp,
                            lineHeight = 16.sp
                        )
                    }
                }

                // 交互功能已关闭
            }
        },
        confirmButton = {
            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                // Kill 按钮 - 只杀掉 CanaryAim 进程
                TextButton(
                    onClick = {
                        // 杀掉当前终端进程
                        process?.destroyForcibly()
                        isRunning = false
                        // 杀掉所有 CanaryAim 进程
                        stopCanaryAim(context)
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        imageVector = Icons.Filled.Stop,
                        contentDescription = "Kill CanaryAim",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(Modifier.width(4.dp))
                    Text("Kill")
                }

                TextButton(
                    onClick = {
                        // 关闭按钮：根据后台模式决定行为
                        if (backgroundModeEnabled) {
                            // 后台模式开启：只关闭对话框，程序继续运行
                            if (isRunning) {
                                // 如果程序正在运行，转为后台运行
                                Thread {
                                    try {
                                        val targetFile = File(context.filesDir, "CanaryAim")
                                        val command = "cd ${context.filesDir.absolutePath} && nohup setsid env LD_LIBRARY_PATH=${context.applicationInfo.nativeLibraryDir}:\$LD_LIBRARY_PATH ${targetFile.absolutePath} </dev/null >/dev/null 2>&1 &"
                                        ProcessBuilder("su", "-c", command).start()
                                        Log.d("CanaryAim", "程序已转为后台运行")
                                    } catch (e: Exception) {
                                        Log.e("CanaryAim", "后台启动失败: ${e.message}", e)
                                    }
                                }.start()
                            }
                            onDismiss()
                        } else {
                            // 后台模式关闭：杀掉整个APP
                            killApp(context)
                        }
                    }
                ) {
                    Text(
                        if (backgroundModeEnabled) "后台运行"
                        else "关闭APP"
                    )
                }
            }
        }
    )
}

fun getManagerVersion(context: Context): Pair<String, Long> {
    val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)!!
    val versionCode = PackageInfoCompat.getLongVersionCode(packageInfo)
    return Pair(packageInfo.versionName!!, versionCode)
}

/**
 * 执行CanaryAim ELF程序
 */
fun executeCanaryAim(context: Context) {
    // 直接在后台执行，不阻塞UI
    Thread {
        try {
            // 1. 从jniLibs复制文件到files目录（而不是cache目录）
            val sourceFileName = "CanaryAim.so" // jniLibs中的文件名
            val targetFileName = "CanaryAim"
            val filesDir = context.filesDir // 使用filesDir而不是cacheDir
            val targetFile = File(filesDir, targetFileName)

            Log.d("CanaryAim", "文件目录: ${filesDir.absolutePath}")
            Log.d("CanaryAim", "目标文件: ${targetFile.absolutePath}")

            // 检查目标文件是否已存在且大小正确
            val nativeLibDir = context.applicationInfo.nativeLibraryDir
            val sourceFile = File(nativeLibDir, sourceFileName)

            if (!sourceFile.exists()) {
                Log.e("CanaryAim", "源文件不存在: ${sourceFile.absolutePath}")
                return@Thread
            }

            // 只在文件不存在或大小不匹配时才复制
            if (!targetFile.exists() || targetFile.length() != sourceFile.length()) {
                Log.d("CanaryAim", "复制文件...")
                sourceFile.copyTo(targetFile, overwrite = true)
                Log.d("CanaryAim", "文件复制完成，大小: ${targetFile.length()} bytes")
            } else {
                Log.d("CanaryAim", "文件已存在，跳过复制")
            }

            // 2. 快速设置执行权限
            if (!targetFile.canExecute()) {
                ProcessBuilder("chmod", "755", targetFile.absolutePath).start().waitFor()
                Log.d("CanaryAim", "已设置执行权限")
            }

            // 3. 启动程序 (后台模式)
            val logFile = File(filesDir, "canary_output.log")
            val command = "cd ${filesDir.absolutePath} && nohup setsid env LD_LIBRARY_PATH=${context.applicationInfo.nativeLibraryDir}:\$LD_LIBRARY_PATH ${targetFile.absolutePath} </dev/null >${logFile.absolutePath} 2>&1 &"

            ProcessBuilder("su", "-c", command).start()

        } catch (e: Exception) {
            Log.e("CanaryAim", "启动失败: ${e.message}", e)
        }
    }.start()
}

@Preview
@Composable
private fun StatusCardPreview() {
    Column {
        StatusCard(KernelVersion(5, 10, 101), 1, null)
        StatusCard(KernelVersion(5, 10, 101), 20000, true)
        StatusCard(KernelVersion(5, 10, 101), null, true)
        StatusCard(KernelVersion(4, 10, 101), null, false)
    }
}

@Preview
@Composable
private fun WarningCardPreview() {
    Column {
        WarningCard(message = "Warning message")
        WarningCard(
            message = "Warning message ",
            MaterialTheme.colorScheme.outlineVariant,
            onClick = {})
    }
}

@Composable
fun ModeButtonsCard(
    backgroundMode: Boolean,
    screenRecordMode: Boolean,
    drawMode: Boolean,
    cardKey: String,
    onBackgroundModeChange: (Boolean) -> Unit,
    onScreenRecordModeChange: (Boolean) -> Unit,
    onDrawModeChange: (Boolean) -> Unit,
    onCardKeyChange: (String) -> Unit
) {
    val context = LocalContext.current

    // 当设置改变时保存
    LaunchedEffect(backgroundMode, screenRecordMode, drawMode) {
        saveSettings(context, screenRecordMode, backgroundMode, drawMode)
    }

    // 当卡密改变时保存
    LaunchedEffect(cardKey) {
        if (cardKey.isNotEmpty()) {
            saveCardKey(cardKey)
        }
    }

    // 卡密输入对话框状态
    var showCardKeyDialog by remember { mutableStateOf(false) }

    // 卡密输入对话框
    if (showCardKeyDialog) {
        CardKeyInputDialog(
            currentKey = cardKey,
            onConfirm = { newKey ->
                onCardKeyChange(newKey)
                showCardKeyDialog = false
            },
            onDismiss = { showCardKeyDialog = false }
        )
    }

    Column {
        // 卡密设置 - 点击式UI
        Spacer(Modifier.height(16.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { showCardKeyDialog = true },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Filled.Key,
                contentDescription = "卡密",
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.size(24.dp)
            )
            Spacer(Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "卡密设置",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = if (cardKey.isNotEmpty()) "已设置" else "点击设置卡密",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Icon(
                imageVector = Icons.Filled.Edit,
                contentDescription = "编辑卡密",
                tint = MaterialTheme.colorScheme.primary
            )
        }
        // 录屏模式 - Switch开关 (对应文件中的output)
        Spacer(Modifier.height(16.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Filled.Videocam,
                contentDescription = "录屏",
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.size(24.dp)
            )
            Spacer(Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "录屏模式",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = if (screenRecordMode) "已开启" else "已关闭",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Switch(
                checked = screenRecordMode,
                onCheckedChange = onScreenRecordModeChange
            )
        }

        // 后台模式 - Switch开关 (对应文件中的choice)
        Spacer(Modifier.height(16.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Filled.CloudQueue,
                contentDescription = "后台",
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.size(24.dp)
            )
            Spacer(Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "后台模式",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = if (backgroundMode) "已开启" else "已关闭",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Switch(
                checked = backgroundMode,
                onCheckedChange = onBackgroundModeChange
            )
        }

        // 绘制模式 - Switch开关 (对应文件中的mode)
        Spacer(Modifier.height(16.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Filled.Brush,
                contentDescription = "绘制",
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.size(24.dp)
            )
            Spacer(Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "单透模式",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = if (drawMode) "已开启" else "已关闭",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Switch(
                checked = drawMode,
                onCheckedChange = onDrawModeChange
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CardKeyInputDialog(
    currentKey: String,
    onConfirm: (String) -> Unit,
    onDismiss: () -> Unit
) {
    var inputKey by remember { mutableStateOf(currentKey) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "卡密设置",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.SemiBold
            )
        },
        text = {
            Column {
                Text(
                    text = "请输入您的卡密：",
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(Modifier.height(16.dp))
                OutlinedTextField(
                    value = inputKey,
                    onValueChange = { inputKey = it },
                    label = { Text("卡密") },
                    placeholder = { Text("请输入卡密") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onConfirm(inputKey) },
                enabled = inputKey.isNotBlank()
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 保存设置到.settings.conf文件
 */
fun saveSettings(context: Context, output: Boolean, choice: Boolean, mode: Boolean) {
    Thread {
        try {
            val settingsFile = File(context.filesDir, ".settings.conf")
            val outputValue = if (output) 2 else 1
            val choiceValue = if (choice) 2 else 1
            val modeValue = if (mode) 2 else 1

            settingsFile.writeText("$outputValue $choiceValue $modeValue")
        } catch (e: Exception) {
            Log.e("Settings", "保存设置失败: ${e.message}", e)
        }
    }.start()
}

/**
 * 加载设置从.settings.conf文件
 */
fun loadSettings(context: Context, callback: (Boolean, Boolean, Boolean) -> Unit) {
    Thread {
        try {
            val settingsFile = File(context.filesDir, ".settings.conf")
            if (settingsFile.exists()) {
                val content = settingsFile.readText().trim()
                val values = content.split(" ")
                if (values.size >= 3) {
                    val output = values[0].toIntOrNull() == 2
                    val choice = values[1].toIntOrNull() == 2
                    val mode = values[2].toIntOrNull() == 2

                    // 在主线程中更新UI
                    Handler(Looper.getMainLooper()).post {
                        callback(output, choice, mode)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("Settings", "加载设置失败: ${e.message}", e)
        }
    }.start()
}

/**
 * 保存卡密到/sdcard/.Login文件
 */
fun saveCardKey(cardKey: String) {
    Thread {
        try {
            // 使用root权限强制创建文件并写入卡密
            val commands = arrayOf(
                "rm -f /sdcard/.Login",  // 先删除可能存在的文件
                "touch /sdcard/.Login",  // 创建新文件
                "echo '$cardKey' > /sdcard/.Login",  // 写入卡密
                "chmod 644 /sdcard/.Login"  // 设置文件权限
            )

            // 逐个执行命令
            commands.forEach { command ->
                ProcessBuilder("su", "-c", command).start().waitFor()
            }
        } catch (e: Exception) {
            // 静默处理异常
        }
    }.start()
}

/**
 * 加载卡密从/sdcard/.Login文件
 */
fun loadCardKey(callback: (String) -> Unit) {
    Thread {
        try {
            // 先检查文件是否存在
            val checkCommand = "test -f /sdcard/.Login && echo 'exists' || echo 'not_exists'"
            val checkProcess = ProcessBuilder("su", "-c", checkCommand).start()
            checkProcess.waitFor()

            val checkReader = BufferedReader(InputStreamReader(checkProcess.inputStream))
            val checkResult = checkReader.readText().trim()
            checkReader.close()

            if (checkResult == "exists") {
                // 文件存在，读取内容
                val readCommand = "cat /sdcard/.Login"
                val readProcess = ProcessBuilder("su", "-c", readCommand).start()
                val readResult = readProcess.waitFor()

                if (readResult == 0) {
                    val reader = BufferedReader(InputStreamReader(readProcess.inputStream))
                    val cardKey = reader.readText().trim()
                    reader.close()

                    // 只有卡密不为空时才回调
                    if (cardKey.isNotEmpty()) {
                        Handler(Looper.getMainLooper()).post {
                            callback(cardKey)
                        }
                    }
                }
            }
            // 如果文件不存在或为空，不执行回调，保持UI默认状态
        } catch (e: Exception) {
            // 静默处理异常
        }
    }.start()
}

/**
 * 杀掉整个APP（不杀运行的二进制程序）
 */
fun killApp(context: Context) {
    Thread {
        try {
            // 不再需要清理日志文件，因为我们使用直接重定向输出

            // 杀掉当前APP进程（不杀二进制程序）
            val packageName = context.packageName
            val killAppCommand = "am force-stop $packageName"
            ProcessBuilder("su", "-c", killAppCommand).start().waitFor()

        } catch (e: Exception) {
            // 如果root命令失败，使用系统方法退出
            try {
                android.os.Process.killProcess(android.os.Process.myPid())
            } catch (ex: Exception) {
                System.exit(0)
            }
        }
    }.start()
}

/**
 * 停止CanaryAim程序
 */
fun stopCanaryAim(context: Context) {
    Thread {
        try {
            // 杀掉CanaryAim进程
            val killCommand = "pkill -f CanaryAim"
            ProcessBuilder("su", "-c", killCommand).start().waitFor()

            // 不再需要清理日志文件，因为我们使用直接重定向输出

        } catch (e: Exception) {
            Log.e("CanaryAim", "停止程序失败: ${e.message}", e)
        }
    }.start()
}
