<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="ic_launcher_background">#FFFFFFFF</color>
    <string name="about">About</string>
    <string name="about_source_code"><![CDATA[View source code at %1$s]]></string>
    <string name="action">Action</string>
    <string name="allowlist_backup">Backup allowlist</string>
    <string name="allowlist_backup_message">Backup currently configured allowlist.</string>
    <string name="allowlist_restore">Restore allowlist</string>
    <string name="allowlist_restore_message">Restore allowlist from recent backup.</string>
    <string name="app_name" translatable="false">KernelSU Next</string>
    <string name="app_profile_export_to_clipboard">Export to clipboard</string>
    <string name="app_profile_import_export">Import/Export</string>
    <string name="app_profile_import_from_clipboard">Import from clipboard</string>
    <string name="app_profile_template_create">Create template</string>
    <string name="app_profile_template_delete">Delete</string>
    <string name="app_profile_template_description">Description</string>
    <string name="app_profile_template_edit">Edit template</string>
    <string name="app_profile_template_export_empty">Cannot find local template to export!</string>
    <string name="app_profile_template_id">ID</string>
    <string name="app_profile_template_id_exist">Template ID already exists!</string>
    <string name="app_profile_template_id_invalid">Invalid template ID</string>
    <string name="app_profile_template_import_empty">Clipboard is empty!</string>
    <string name="app_profile_template_import_success">Imported successfully</string>
    <string name="app_profile_template_name">Name</string>
    <string name="app_profile_template_readonly">Read only</string>
    <string name="app_profile_template_save">Save</string>
    <string name="app_profile_template_save_failed">Failed to save template</string>
    <string name="app_profile_template_sync">Sync online templates</string>
    <string name="app_profile_template_view">View template</string>
    <string name="backup_restore">Backup &amp; Restore</string>
    <string name="cancel">Cancel</string>
    <string name="close">Close</string>
    <string name="confirm">Confirm</string>
    <string name="customization">Customization</string>
    <string name="developer">Developer</string>
    <string name="direct_install">Direct install (Recommended)</string>
    <string name="disable">Disable</string>
    <string name="disabled">Disabled</string>
    <string name="enable">Enable</string>
    <string name="enable_developer_options">Enable developer options</string>
    <string name="enable_developer_options_summary">Show hidden settings and debug info relevant only for developers.</string>
    <string name="enable_web_debugging">Enable WebView debugging</string>
    <string name="enable_web_debugging_summary">Can be used to debug WebUI. Please enable only when needed.</string>
    <string name="enabled">Enabled</string>
    <string name="export_log">Export logs</string>
    <string name="failed_to_update_app_profile">Failed to update App Profile for %s</string>
    <string name="failed_to_update_sepolicy">Failed to update SELinux rules for %s</string>
    <string name="flash_failed">Flash failed</string>
    <string name="flash_success">Flash success</string>
    <string name="flashing">Flashing</string>
    <string name="force_stop_app">Force stop</string>
    <string name="grant_root_failed">Failed to grant root!</string>
    <string name="hide_system_apps">Hide system apps</string>
    <string name="home">Home</string>
    <string name="home_abi">ABI</string>
    <string name="home_android">Android version</string>
    <string name="home_click_to_install">点击获取Root</string>
    <string name="home_experimental_kernelsu">⚠️ Experimental development warning!</string>
    <string name="home_experimental_kernelsu_body">KernelSU Next is a non-official version that is always under active experimental development. It\'s provided as-is, with no guarantees of stability, performance, or reliability.</string>
    <string name="home_experimental_kernelsu_body_point_1"> • Use at your own risk: crashes, unexpected behavior, or system issues may occur.</string>
    <string name="home_experimental_kernelsu_body_point_2"> • No warranty: the developers aren\'t responsible for any data loss, system damage, or other consequences resulting from its use.</string>
    <string name="home_experimental_kernelsu_body_point_3"> • For testing purposes only: intended for users who understand the risks and are comfortable troubleshooting issues.</string>
    <string name="home_experimental_kernelsu_repo">127.0.0.1</string>
    <string name="home_failure">KernelSU Next v2 signature not found in kernel! [ !KSU_NEXT || != size/hash ]</string>
    <string name="home_failure_tip">Ask your kernel developer to integrate KernelSU Next!</string>
    <string name="home_kernel">Kernel version</string>
    <string name="home_magic_mount">Magic Mount</string>
    <string name="home_manager_version">Manager version</string>
    <string name="home_module_count_plural">Modules</string>
    <string name="home_module_count_singular">Module</string>
    <string name="home_module_update_count">Updates: %d</string>
    <string name="home_mount_system">Mount system</string>
    <string name="home_next_kernelsu">🔥 Next build</string>
    <string name="home_next_kernelsu_body">Next experimental branch. Check it out on GitHub!</string>
    <string name="home_next_kernelsu_repo">https://github.com/KernelSU-Next/KernelSU-Next</string>
    <string name="home_not_installed">未获取Root</string>
    <string name="home_overlayfs_mount">OverlayFS</string>
    <string name="home_selinux_status">SELinux status</string>
    <string name="home_superuser_count_plural">Superusers</string>
    <string name="home_superuser_count_singular">Superuser</string>
    <string name="home_susfs">SuSFS: %s</string>
    <string name="home_susfs_sus_su">SuS SU</string>
    <string name="home_susfs_version">SuSFS version</string>
    <string name="home_working">Working</string>
    <string name="home_working_version">Version: %d</string>
    <string name="hook_mode">Hook mode</string>
    <string name="install">Install</string>
    <string name="install_inactive_slot">Install to inactive slot (After OTA)</string>
    <string name="install_inactive_slot_warning">Your device will be **FORCED** to boot to the current inactive slot after a reboot!\nOnly use this option after OTA is done.\nContinue?</string>
    <string name="install_next">Next</string>
    <string name="issue_report_body">Encountered a bug or have feedback?</string>
    <string name="issue_report_body_2">Report it as soon as possible!</string>
    <string name="issue_report_github">Report on GitHub</string>
    <string name="issue_report_github_link">https://github.com/KernelSU-Next/KernelSU-Next/issues</string>
    <string name="issue_report_telegram">Contact via Telegram</string>
    <string name="issue_report_telegram_link">https://t.me/ksunext</string>
    <string name="issue_report_title">Having trouble?</string>
    <string name="later">Later</string>
    <string name="launch_app">Launch</string>
    <string name="lkm_alternative_suggestion">Install GKI kernel or integrate KernelSU Next to your device.</string>
    <string name="lkm_mode_deprecated">LKM mode is now deprecated!</string>
    <string name="lkm_warning_message">The LKM patch relies on closed source components. Do you want to continue?</string>
    <string name="log_saved">Logs saved</string>
    <string name="module">Module</string>
    <string name="module_author">Author</string>
    <string name="module_backup">Backup module</string>
    <string name="module_backup_message">Backup currently installed modules.</string>
    <string name="module_changelog">Changelog</string>
    <string name="module_changelog_failed">Fetch changelog failed: %s</string>
    <string name="module_downloading">Downloading module: %s</string>
    <string name="module_empty">No module installed</string>
    <string name="module_failed_to_disable">Failed to disable module: %s</string>
    <string name="module_failed_to_enable">Failed to enable module: %s</string>
    <string name="module_id">ID</string>
    <string name="module_install">Install</string>
    <string name="module_install_prompt_with_name">The following modules will be installed: %1$s</string>
    <string name="module_magisk_conflict">Modules are unavailable due to a conflict with Magisk!</string>
    <string name="module_overlay_fs_not_available">Modules are unavailable as OverlayFS is disabled by the kernel!</string>
    <string name="module_restore">Restore module</string>
    <string name="module_restore_confirm">Are you sure you want to restore module %s?</string>
    <string name="module_restore_failed">Failed to restore: %s</string>
    <string name="module_restore_message">Restore modules from recent backup.</string>
    <string name="module_restore_success">%s restored</string>
    <string name="module_size_high_to_low">Sort (High → Low)</string>
    <string name="module_size_low_to_high">Sort (Low → High)</string>
    <string name="module_sort_a_to_z">Sort (A → Z)</string>
    <string name="module_sort_action_first">Sort (Action first)</string>
    <string name="module_sort_enabled_first">Sort (Enabled first)</string>
    <string name="module_sort_webui_first">Sort (WebUI first)</string>
    <string name="module_sort_z_to_a">Sort (Z → A)</string>
    <string name="module_start_downloading">Start downloading: %s</string>
    <string name="module_uninstall_confirm">Are you sure you want to uninstall module %s?</string>
    <string name="module_uninstall_failed">Failed to uninstall: %s</string>
    <string name="module_uninstall_success">%s uninstalled</string>
    <string name="module_update">Update</string>
    <string name="module_update_available">Update</string>
    <string name="module_update_json">UpdateJson</string>
    <string name="module_update_json_empty">Empty</string>
    <string name="module_updated">Updated</string>
    <string name="module_version">Version</string>
    <string name="module_version_code">Code</string>
    <string name="new_version_available">New version %s is available, click to upgrade.</string>
    <string name="open">Open</string>
    <string name="proceed">Proceed</string>
    <string name="profile" translatable="false">App Profile</string>
    <string name="profile_capabilities">Capabilities</string>
    <string name="profile_custom">Custom</string>
    <string name="profile_default">Default</string>
    <string name="profile_groups">Groups</string>
    <string name="profile_name">Profile name</string>
    <string name="profile_namespace">Mount namespace</string>
    <string name="profile_namespace_global">Global</string>
    <string name="profile_namespace_individual">Individual</string>
    <string name="profile_namespace_inherited">Inherited</string>
    <string name="profile_selinux_context">SELinux context</string>
    <string name="profile_selinux_domain">Domain</string>
    <string name="profile_selinux_rules">Rules</string>
    <string name="profile_template">Template</string>
    <string name="profile_umount_modules">Umount modules</string>
    <string name="profile_umount_modules_summary">Enabling this option will allow KernelSU Next to restore any modified files by the modules for this app.</string>
    <string name="reboot">Reboot</string>
    <string name="reboot_bootloader">Reboot to Bootloader</string>
    <string name="reboot_download">Reboot to Download</string>
    <string name="reboot_edl">Reboot to EDL</string>
    <string name="reboot_message">The changes will take effect after the system restart. Do you want to reboot now?</string>
    <string name="reboot_recovery">Reboot to Recovery</string>
    <string name="reboot_required">Reboot required</string>
    <string name="reboot_to_apply">Reboot to take effect</string>
    <string name="reboot_userspace">Soft reboot</string>
    <string name="refresh">Refresh</string>
    <string name="require_kernel_version">The current KernelSU Next version %1$d is too low for the manager to work properly. Please upgrade to version %2$d or higher!</string>
    <string name="restart_app">Restart</string>
    <string name="restart_app_message">The app needs to restart for this change to take effect.</string>
    <string name="restart_required">Restart required</string>
    <string name="restore">Restore</string>
    <string name="safe_mode">Safe mode</string>
    <string name="save_log">Save logs</string>
    <string name="select_file">Select a file</string>
    <string name="select_file_tip">%1$s partition image is recommended</string>
    <string name="select_kmi">Select KMI</string>
    <string name="selected_lkm">Selected LKM: %s</string>
    <string name="selinux_status_disabled">Disabled</string>
    <string name="selinux_status_enforcing">Enforcing</string>
    <string name="selinux_status_permissive">Permissive</string>
    <string name="selinux_status_unknown">Unknown</string>
    <string name="send_log">Share logs</string>
    <string name="settings">Settings</string>
    <string name="settings_amoled_mode">AMOLED mode</string>
    <string name="settings_amoled_mode_summary">Enable a pure black theme useful for AMOLED screens to reduce eye strain and save battery.</string>
    <string name="settings_banner">Enable banners</string>
    <string name="settings_banner_summary">Show background banners for modules.</string>
    <string name="settings_check_update">Check for updates</string>
    <string name="settings_check_update_summary">Automatically check for updates when opening the app</string>
    <string name="settings_disable_su">Disable su compatibility</string>
    <string name="settings_disable_su_summary">Temporarily disable the ability of any app to gain root privileges via the ⁠su command (Existing root processes won\'t be affected).</string>
    <string name="settings_language">Language</string>
    <string name="settings_legacyui">Use legacy UI</string>
    <string name="settings_legacyui_summary">Switch to the previous user interface style.</string>
    <string name="settings_profile_template">App Profile template</string>
    <string name="settings_profile_template_summary">Manage local and online template of App Profile</string>
    <string name="settings_restore_stock_image">Restore stock image</string>
    <string name="settings_restore_stock_image_message">Restore the stock factory image (If a backup exists), usually used before OTA; if you need to uninstall KernelSU Next, please use \"Uninstall permanently\".</string>
    <string name="settings_susfs_toggle">Hide kprobes hook</string>
    <string name="settings_susfs_toggle_summary">This option disables the kprobes hook created by ksu and, instead, activates the embedded non-kprobes hook, implementing the same functionality that would be applied to a non-GKI kernel, which doesn\'t support kprobe.</string>
    <string name="settings_umount_modules_default">Umount modules</string>
    <string name="settings_umount_modules_default_summary">The global default value for \"Umount modules\" in App Profile. If enabled, it will remove all module modifications to the system for apps that don\'t have a profile set.</string>
    <string name="settings_uninstall">Uninstall</string>
    <string name="settings_uninstall_permanent">Uninstall permanently</string>
    <string name="settings_uninstall_permanent_message">Uninstalling KernelSU Next (Root and all modules) completely and permanently.</string>
    <string name="settings_uninstall_temporary">Uninstall temporarily</string>
    <string name="settings_uninstall_temporary_message">Temporarily uninstall KernelSU Next, restore to original state after next reboot.</string>
    <string name="show_system_apps">Show system apps</string>
    <string name="shrink_sparse_image">Minimize sparse image</string>
    <string name="shrink_sparse_image_message">Resize the sparse image where the module is located to its actual size. Note that this may cause the module to work abnormally, so please only use when necessary (Such as for backup).</string>
    <string name="su_not_allowed">Couldn\'t grant Superuser access to %s</string>
    <string name="sucompat_disabled">SUCOMPAT DISABLED</string>
    <string name="superuser">Superuser</string>
    <string name="susfs_supported">Supported</string>
    <string name="system_default">System default</string>
    <string name="unavailable">Unavailable</string>
    <string name="uninstall">Uninstall</string>
    <string name="uninstalled">Uninstalled</string>
    <string name="use_overlay_fs">Use OverlayFS</string>
    <string name="use_overlay_fs_summary">Toggle between using OverlayFS over Magic Mount for KernelSU Next\'s mount system.</string>
    <string name="use_webuix">Use WebUI X</string>
    <string name="use_webuix_eruda">Inject Eruda into WebUI X</string>
    <string name="use_webuix_eruda_summary">Inject a debug console into WebUI X to make debugging easier. Requires web debugging to be on.</string>
    <string name="use_webuix_summary">Use WebUI X instead of WebUI, which supports more APIs.</string>
    <string name="warning">Warning</string>
    <string name="warning_message">This feature is still in beta and under development. Please ensure you backup your modules before proceeding. Only use this feature if you understand the potential risks. Proceed with caution.</string>
    <string name="webui">WebUI</string>
    <string name="zygisk_required">Zygisk required</string>
    <string name="zygisk_status">Zygisk injection</string>
    <style name="Theme.KernelSU" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowLayoutInDisplayCutoutMode" ns1:targetApi="o_mr1">shortEdges</item>
    </style>
    <style name="Theme.KernelSU.WebUI" parent="Theme.KernelSU">
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="o_mr1">true</item>
    </style>
</resources>